/* eslint-disable theme-colors/no-literal-colors */
import { t } from '@superset-ui/core';
import { Tooltip } from 'src/components/Tooltip';

export interface Feedback {
  id: number;
  user_id?: number | null;
  dashboard_id: number;
  rating?: number | null;
  standalone: boolean;
  comment?: string | null;
  created_on?: string | null;
}

export const getTableColumns = () => [
  {
    title: t('Date'),
    dataIndex: 'created_on',
    key: 'created_on',
    width: 100,
    sorter: (a: Feedback, b: Feedback) => {
      if (!a.created_on || !b.created_on) return 0;
      return (
        new Date(a.created_on).getTime() - new Date(b.created_on).getTime()
      );
    },
    render: (created_on: string | null) => {
      if (!created_on) return '-';
      return created_on.split('T')[0]; // Show just the date part
    },
  },
  {
    title: t('Rating'),
    dataIndex: 'rating',
    key: 'rating',
    width: 110,
    sorter: (a: Feed<PERSON>, b: Feedback) => {
      if (a.rating == null || b.rating == null) return 0;
      return a.rating - b.rating;
    },
    render: (rating: number | null) =>
      rating != null ? `${rating / 2}/5` : '-',
    filters: [
      { text: '0.5', value: 1 },
      { text: '1', value: 2 },
      { text: '1.5', value: 3 },
      { text: '2', value: 4 },
      { text: '2.5', value: 5 },
      { text: '3', value: 6 },
      { text: '3.5', value: 7 },
      { text: '4', value: 8 },
      { text: '4.5', value: 9 },
      { text: '5', value: 10 },
    ],
    onFilter: (value: number, record: Feedback) => record.rating === value,
  },
  {
    title: t('Comment'),
    dataIndex: 'comment',
    key: 'comment',
    render: (comment: string | null) => {
      if (!comment) return '-';
      return (
        <Tooltip title={comment} placement="topLeft">
          {comment}
        </Tooltip>
      );
    },
    filters: [
      { text: t('Has comment'), value: true },
      { text: t('No comment'), value: false },
    ],
    onFilter: (value: boolean, record: Feedback) =>
      Boolean(record.comment) === value,
  },
  {
    title: t('From'),
    dataIndex: 'standalone',
    key: 'from',
    width: 110,
    render: (standalone: boolean) => (standalone ? 'Standalone' : 'OM'),
    filters: [
      { text: 'OM', value: false },
      { text: 'Standalone', value: true },
    ],
    sorter: (a: Feedback, b: Feedback) =>
      Number(a.standalone) - Number(b.standalone),
    onFilter: (value: boolean, record: Feedback) => record.standalone === value,
  },
];

const getMonthWeek = (dateString: string): string => {
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = date.getMonth(); // 0-based
  const day = date.getDate();

  // Находим первый день месяца
  const firstDayOfMonth = new Date(year, month, 1);

  // Вычисляем номер недели в месяце
  // Неделя начинается с понедельника
  const firstDayWeekday = firstDayOfMonth.getDay(); // 0 = воскресенье, 1 = понедельник
  const adjustedFirstDay = firstDayWeekday === 0 ? 6 : firstDayWeekday - 1; // Преобразуем в 0 = понедельник

  // Номер недели в месяце
  const weekNumber = Math.ceil((day + adjustedFirstDay) / 7);

  const monthStr = String(month + 1).padStart(2, '0'); // 0-based to 1-based

  return `${year}-${monthStr}-W${weekNumber}`;
};

const groupFeedbackByGranularity = (
  feedbacks: Feedback[],
  granularity: Granularity,
) => {
  const result: Record<string, Record<string, number>> = {};

  feedbacks.forEach(feedback => {
    // Skip if created_on or rating is missing
    if (!feedback.created_on || feedback.rating == null) {
      return;
    }

    const date = feedback.created_on.split('T')[0]; // Extract date part from ISO string
    let label = '';

    if (granularity === 'day') {
      label = date; // '2025-04-05'
    } else if (granularity === 'week') {
      label = getMonthWeek(date); // '2025-10-W1'
    } else {
      label = date.slice(0, 7); // '2025-04'
    }

    if (!result[label]) {
      result[label] = {};
    }

    const rating = feedback.rating / 2;

    if (!result[label][rating]) {
      result[label][rating] = 0;
    }

    result[label][rating] += 1;
  });

  return result;
};

const prepareChartData = (
  groupedData: Record<string, Record<string, number>>,
) => {
  const labels = Object.keys(groupedData).sort(); // ['2025-01', ...]

  // Получаем все возможные рейтинги
  const allRatings = new Set<number>();
  Object.values(groupedData).forEach(ratings => {
    Object.keys(ratings).forEach(rating => {
      allRatings.add(Number(rating));
    });
  });

  const seriesData: Record<number, number[]> = {};

  // Инициализируем массивы для всех рейтингов
  Array.from(allRatings).forEach(rating => {
    seriesData[rating] = [];
  });

  // Заполняем данные для каждой метки
  labels.forEach(label => {
    Array.from(allRatings).forEach(rating => {
      const value = groupedData[label][rating] || 0;
      seriesData[rating].push(value);
    });
  });

  return { labels, seriesData };
};

const ratingColors: Record<string, string> = {
  0.5: '#ff6262',
  1: '#ff7868',
  1.5: '#ff8e6e',
  2: '#ffa574',
  2.5: '#ffbb7a',
  3: '#ffd17f',
  3.5: '#ffe68f',
  4: '#d4eb9d',
  4.5: '#aad6a3',
  5: '#65d46b',
};

export type Granularity = 'day' | 'week' | 'month';

export const getChartOptions = (
  data: Feedback[],
  granularity: Granularity,
  isStacked: boolean,
) => {
  const groupedData = groupFeedbackByGranularity(data, granularity);
  const { labels, seriesData } = prepareChartData(groupedData);

  // Только для дней используем временную ось с парами [время, значение]
  const series = Object.entries(seriesData)
    .sort((a, b) => Number(a[0]) - Number(b[0]))
    .map(([rating, dataValues]) => {
      let seriesData;

      if (granularity === 'day') {
        // Для дней создаем массив пар [время, значение]
        seriesData = labels.map((label, index) => {
          const timeValue = new Date(label).getTime();
          return [timeValue, dataValues[index]];
        });
      } else {
        // Для недель и месяцев используем обычные значения
        seriesData = dataValues;
      }

      return {
        name: `${t('Rating')} ${rating}`,
        type: 'bar',
        stack: isStacked ? 'Total' : rating,
        itemStyle: { color: ratingColors[rating] },
        data: seriesData,
      };
    });

  const xAxisConfig =
    granularity === 'day'
      ? {
          type: 'time' as const,
          axisLabel: {
            rotate: 45,
            interval: 'auto',
            formatter: (value: number) => {
              const date = new Date(value);
              return date.toLocaleDateString('ru-RU', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
              });
            },
          },
        }
      : {
          type: 'category' as const,
          data: labels,
        };

  return {
    width: 648,
    height: 400,
    echartOptions: {
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' },
        formatter: (params: any[]) => {
          let label;
          if (granularity === 'day') {
            // Для дней используем axisValueLabel (временная ось)
            label = params[0]?.axisValueLabel;
            if (label) {
              const date = new Date(label);
              label = date.toLocaleDateString('ru-RU', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
              });
            }
          } else {
            // Для недель и месяцев используем name (категориальная ось)
            label = params[0]?.name;
          }

          let result = `<strong>${label}</strong><br/>`;
          let total = 0;

          params.forEach(param => {
            if (!param.seriesName.startsWith(t('Rating'))) return;
            const { value } = param;
            const actualValue = Array.isArray(value) ? value[1] : value;
            if (actualValue) {
              total += actualValue;
              result += `
                <span style="display:inline-block;margin:2px 0;">
                  <span style="display:inline-block;margin-right:8px;border-radius:10px;width:10px;height:10px;background:${param.color}"></span>
                  <strong>${param.seriesName}</strong>: ${actualValue}
                </span><br/>
              `;
            }
          });

          result += `<hr style="margin:5px 0;border:0;border-top:1px solid #ccc"/><strong>Total:</strong> ${total}`;
          return result;
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: xAxisConfig,
      yAxis: {
        type: 'value',
        min: 0,
        max: 'dataMax',
      },
      series,
    },
    selectedValues: {},
    refs: {},
  };
};
