import { useEffect, useState } from 'react';
import { SupersetClient, t, styled } from '@superset-ui/core';
import { useToasts } from 'src/components/MessageToasts/withToasts';
import Loading from 'src/components/Loading';

const Container = styled.div`
  position: relative;
  margin-bottom: ${({ theme }) => theme.gridUnit * 8}px;
  min-height: 70px;
`;

const GridContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 4px 8px;

  p {
    margin: 0;
  }
`;

const FlexRow = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const Bold = styled.span`
  font-weight: ${({ theme }) => theme.typography.weights.bold};
`;

interface SummaryData {
  total: { average: number; count: number };
  plugin: { average: number; count: number };
  standalone: { average: number; count: number };
}

const Summary = ({ dashboardId }: { dashboardId: number }) => {
  const [data, setData] = useState<SummaryData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { addDangerToast } = useToasts();

  useEffect(() => {
    async function fetchData() {
      try {
        setIsLoading(true);
        const response = await SupersetClient.get({
          endpoint: `/api/v1/csi_feedback/summary?dashboard_id=${dashboardId}`,
        });

        const summaryData = response.json.result || null;

        setData(summaryData);
      } catch {
        addDangerToast(t('Failed to fetch dashboard CSI summary.'));
      } finally {
        setIsLoading(false);
      }
    }

    fetchData();
  }, [addDangerToast, dashboardId]);

  return (
    <Container>
      {isLoading && <Loading />}
      {data && (
        <>
          <Bold>{t('Summary')}:</Bold>
          <GridContainer>
            <div>
              <p>
                {t('Total feedbacks')}: <Bold>{data.total.count}</Bold>
              </p>
              <FlexRow>
                <p>
                  Standalone: <Bold>{data.standalone.count}</Bold>
                </p>
                <p>
                  OM: <Bold>{data.plugin.count}</Bold>
                </p>
              </FlexRow>
            </div>
            <div>
              <p>
                {t('Average rating')}: <Bold>{data.total.average / 2}</Bold>
              </p>
              <FlexRow>
                <p>
                  Standalone: <Bold>{data.standalone.average / 2}</Bold>
                </p>
                <p>
                  OM: <Bold>{data.plugin.average / 2}</Bold>
                </p>
              </FlexRow>
            </div>
          </GridContainer>
        </>
      )}
    </Container>
  );
};

export default Summary;
