#
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
*.bak
*.db
*.pyc
*.sqllite
*.swp
__pycache__

.local
.cache
.bento*
.cache-loader
.coverage
cover
.DS_Store
.eggs
.env
.envrc
.idea
.mypy_cache
.python-version
.tox
.vscode
superset_home
_build
_images
_modules
_static
build
app.db
apache_superset.egg-info/
changelog.sh
dist
dump.rdb
env
venv*
env_py3
envpy3
env36
local_config.py
/superset_config.py
/superset_text.yml
superset.egg-info/
superset/bin/supersetc
tmp
rat-results.txt
superset/app/
superset-websocket/config.json

# Node.js, webpack artifacts, storybook
*.entry.js
*.js.map
node_modules
npm-debug.log*
superset/static/assets
superset/static/version_info.json
superset-frontend/**/esm/*
superset-frontend/**/lib/*
superset-frontend/**/storybook-static/*
superset-frontend/migration-storybook.log
yarn-error.log
*.map
*.min.js
test-changelog.md
*.tsbuildinfo

# Ignore package-lock in packages
plugins/*/package-lock.json
packages/*/package-lock.json

# For country map geojson conversion script
.ipynb_checkpoints/
scripts/*.zip

# IntelliJ
*.iml
venv
@eaDir/

# PyCharm
.run

# Test data
celery_results.sqlite
celerybeat-schedule
celerydb.sqlite
celerybeat.pid
geckodriver.log
ghostdriver.log
testCSV.csv
.terser-plugin-cache/
apache-superset-*.tar.gz*
release.json

# Translation-related files
# these json files are generated by ./scripts/po2json.sh
# superset/translations/**/messages.json
# these mo binary files are generated by `pybabel compile`
superset/translations/**/messages.mo

docker/requirements-local.txt

cache/
docker/*local*

.temp_cache

# Jest test report
test-report.html

architecture-as-code/node_modules
