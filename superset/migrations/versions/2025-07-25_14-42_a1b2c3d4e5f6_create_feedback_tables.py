# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
"""Create CSI tables

Revision ID: a1b2c3d4e5f6
Revises: 48cbb571fa3a
Create Date: 2025-07-25 14:42:55.000000

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "a1b2c3d4e5f6"
down_revision = "48cbb571fa3a"


def upgrade():
    # Create CSI config table
    op.create_table(
        "csi_config",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("dashboard_id", sa.Integer(), nullable=False),
        sa.Column("standalone_enabled", sa.Boolean(), nullable=False, default=False),
        sa.Column("plugin_enabled", sa.Boolean(), nullable=False, default=False),
        sa.Column("created_on", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["dashboard_id"],
            ["dashboards.id"],
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("dashboard_id"),
    )

    # Create CSI table
    op.create_table(
        "csi",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=True),
        sa.Column("dashboard_id", sa.Integer(), nullable=False),
        sa.Column("rating", sa.Integer(), nullable=True),
        sa.Column("standalone", sa.Boolean(), nullable=False, default=False),
        sa.Column("comment", sa.String(length=500), nullable=True),
        sa.Column("voter_hash", sa.String(length=64), nullable=True),
        sa.Column("created_on", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["ab_user.id"],
            ondelete="SET NULL",
        ),
        sa.ForeignKeyConstraint(
            ["dashboard_id"],
            ["dashboards.id"],
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("id"),
    )

    # Create indexes
    op.create_index(
        "idx_csi_config_dashboard_id",
        "csi_config",
        ["dashboard_id"],
    )
    op.create_index(
        "idx_csi_dashboard_id",
        "csi",
        ["dashboard_id"],
    )
    op.create_index(
        "idx_csi_user_id",
        "csi",
        ["user_id"],
    )
    op.create_index(
        "idx_csi_voter_hash",
        "csi",
        ["voter_hash"],
    )


def downgrade():
    op.drop_index("idx_csi_voter_hash", table_name="csi")
    op.drop_index("idx_csi_dashboard_id", table_name="csi")
    op.drop_index("idx_csi_user_id", table_name="csi")
    op.drop_table("csi")

    op.drop_index("idx_csi_config_dashboard_id", table_name="csi_config")
    op.drop_table("csi_config")
