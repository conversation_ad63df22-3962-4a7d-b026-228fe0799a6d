# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

"""Root level routes for static files and common endpoints."""

import os
from flask import Blueprint, redirect, Response, send_from_directory, current_app
from flask.typing import ResponseReturnValue

# Create blueprint for root level routes (without /superset prefix)
root_routes_bp = Blueprint("root_routes", __name__)


@root_routes_bp.route("/robots.txt")
def robots_txt() -> ResponseReturnValue:
    """Serve robots.txt file"""
    robots_content = """User-agent: *
Disallow: /api/
Disallow: /superset/explore/
Disallow: /superset/dashboard/
Allow: /
"""
    return Response(robots_content, mimetype="text/plain")


@root_routes_bp.route("/favicon.ico")
def favicon() -> ResponseReturnValue:
    """Serve favicon.ico from static assets"""
    return redirect("/static/assets/images/favicon.png")


@root_routes_bp.route("/apple-touch-icon.png")
@root_routes_bp.route("/apple-touch-icon-precomposed.png")
def apple_touch_icon() -> ResponseReturnValue:
    """Serve apple touch icon from static assets"""
    return redirect("/static/assets/images/favicon.png")


@root_routes_bp.route("/static/appbuilder/<path:filename>")
def appbuilder_static(filename: str) -> ResponseReturnValue:
    """Serve Flask-AppBuilder static files"""
    # Get the path to superset/static/appbuilder directory
    static_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "static", "appbuilder")
    return send_from_directory(static_dir, filename)
