# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

from typing import Any

from superset.csi.models import CSI, CSIConfig
from superset.daos.base import BaseDAO
from superset.extensions import db


class CSIDAO(BaseDAO[CSI]):
    """Data Access Object for CSI operations"""

    @classmethod
    def find_by_dashboard_id(cls, dashboard_id: int) -> list[CSI]:
        """Get all CSI entries for a specific dashboard"""
        return (
            db.session.query(cls.model_cls)
            .filter(cls.model_cls.dashboard_id == dashboard_id)
            .all()
        )

    @classmethod
    def find_by_voter_hash(cls, voter_hash: str) -> CSI | None:
        """Find CSI entry by voter hash"""
        return (
            db.session.query(cls.model_cls)
            .filter(cls.model_cls.voter_hash == voter_hash)
            .first()
        )

    @classmethod
    def get_summary(cls, dashboard_id: int) -> dict[str, Any]:
        """Get summary of feedback entries for a specific dashboard"""
        data: list[CSI] = (
            db.session.query(cls.model_cls)
            .filter(cls.model_cls.dashboard_id == dashboard_id)
            .all()
        )
        standalone = [csi for csi in data if csi.standalone]
        plugin = [csi for csi in data if not csi.standalone]

        return {
            "standalone": {
                "average": round(
                    sum(csi.rating for csi in standalone) / len(standalone), 1
                )
                if standalone
                else 0,
                "count": len(standalone),
            },
            "plugin": {
                "average": round(sum(csi.rating for csi in plugin) / len(plugin), 1)
                if plugin
                else 0,
                "count": len(plugin),
            },
            "total": {
                "average": round(sum(csi.rating for csi in data) / len(data), 1)
                if data
                else 0,
                "count": len(data),
            },
        }

    @classmethod
    def create_feedback(  # pylint: disable=too-many-arguments
        cls,
        user_id: int | None,
        dashboard_id: int,
        rating: int,
        standalone: bool,
        comment: str | None,
        voter_hash: str,
    ) -> CSI:
        """Create a new CSI entry"""
        csi = cls.create(
            attributes={
                "user_id": user_id,
                "dashboard_id": dashboard_id,
                "rating": rating,
                "standalone": standalone,
                "comment": comment,
                "voter_hash": voter_hash,
            }
        )
        db.session.commit()  # pylint: disable=consider-using-transaction
        return csi


class CSIConfigDAO(BaseDAO[CSIConfig]):
    """Data Access Object for CSI Configuration operations"""

    @classmethod
    def find_by_dashboard_id(cls, dashboard_id: int) -> CSIConfig | None:
        """Get CSI configuration for a specific dashboard"""
        return cls.find_one_or_none(dashboard_id=dashboard_id)

    @classmethod
    def create_or_update(
        cls,
        dashboard_id: int,
        standalone_enabled: bool,
        plugin_enabled: bool,
    ) -> CSIConfig:
        """Create or update CSI configuration for a dashboard"""

        if config := cls.find_by_dashboard_id(dashboard_id):
            updated_config = cls.update(
                config,
                {
                    "standalone_enabled": standalone_enabled,
                    "plugin_enabled": plugin_enabled,
                },
            )
            db.session.commit()  # pylint: disable=consider-using-transaction
            return updated_config

        new_config = cls.create(
            attributes={
                "dashboard_id": dashboard_id,
                "standalone_enabled": standalone_enabled,
                "plugin_enabled": plugin_enabled,
            }
        )
        db.session.commit()  # pylint: disable=consider-using-transaction
        return new_config
