{"domain": "superset", "locale_data": {"superset": {"22": ["22"], "": {"domain": "superset", "plural_forms": "nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : 2);", "lang": "ru"}, "The datasource is too large to query.": ["Источник данных слишком велик для запроса."], "The database is under an unusual load.": ["База данных испытывает необычную нагрузку."], "The database returned an unexpected error.": ["База данных вернула неожиданную ошибку."], "There is a syntax error in the SQL query. Perhaps there was a misspelling or a typo.": ["В запросе SQL есть синтаксическая ошибка. В SQL-запросе имеется синтаксическая ошибка. Возможно, это орфографическая ошибка или опечатка."], "The column was deleted or renamed in the database.": ["Столбец был удален или переименован в базе данных."], "The table was deleted or renamed in the database.": ["Таблица была удалена или переименована в базе данных."], "One or more parameters specified in the query are missing.": ["Один или несколько параметров, указанных в запросе, отсутствуют"], "The hostname provided can't be resolved.": ["Не удалось обнаружить хост."], "The port is closed.": ["Порт закрыт."], "The host might be down, and can't be reached on the provided port.": ["Хост возможно, отключен, и с ним невозможно связаться по заданному порту."], "Superset encountered an error while running a command.": ["Суперсет столкнулся с ошибкой во время выполнения команды."], "Superset encountered an unexpected error.": ["Суперсет столкнулся с неожиданной ошибкой."], "The username provided when connecting to a database is not valid.": ["Имя пользователя, указанное при подключении к базе данных, недействительно"], "The password provided when connecting to a database is not valid.": ["Неверный пароль для базы данных."], "Either the username or the password is wrong.": ["Неверное имя пользователя или пароль."], "Either the database is spelled incorrectly or does not exist.": ["Неверное или несуществующее имя базы данных."], "The schema was deleted or renamed in the database.": ["Схема была удалена или переименована в базе данных."], "User doesn't have the proper permissions.": ["У пользователя нет надлежащего доступа."], "One or more parameters needed to configure a database are missing.": ["Один или несколько параметров, необходимых для настройки базы данных, отсутствуют"], "The submitted payload has the incorrect format.": ["Загруженные данные имеют некорректный формат."], "The submitted payload has the incorrect schema.": ["Загруженные данные имеют некорректную схему."], "Results backend needed for asynchronous queries is not configured.": ["Сервер, необходимый для асинхронных запросов, не настроен."], "Database does not allow data manipulation.": ["База данных не позволяет изменять свои данные."], "The CTAS (create table as select) doesn't have a SELECT statement at the end. Please make sure your query has a SELECT as its last statement. Then, try running your query again.": ["CTAS (Create Table As Select) не имеет оператора SELECT в конце. CTAS (CREATE TABLE AS SELECT) не содержит SELECT запрос в конце. Пожалуйста, убедитесь, что ваш запрос имеет SELECT запрос в конце. Затем попробуйте повторно выполнить запрос."], "CVAS (create view as select) query has more than one statement.": ["CVAS (CREATE VIEW AS SELECT) запрос содержит больше одного запроса."], "CVAS (create view as select) query is not a SELECT statement.": ["CVAS (CREATE VIEW AS SELECT) запрос не является SELECT запросом."], "Query is too complex and takes too long to run.": ["Запрос слишком тяжелый для выполнения и займет много времени."], "The database is currently running too many queries.": ["В настоящий момент база данных обрабатывает слишком много запросов."], "One or more parameters specified in the query are malformed.": ["Один или несколько параметров, указанных в запросе, являются искаженными."], "The object does not exist in the given database.": ["Объект не существует в этой базе данных."], "The query has a syntax error.": ["Запрос имеет синтаксическую ошибку."], "The results backend no longer has the data from the query.": ["Сервер не сохранил данные из этого запроса."], "The query associated with the results was deleted.": ["Запрос, связанный с результатами, был удален."], "The results stored in the backend were stored in a different format, and no longer can be deserialized.": ["Данные, сохраненные на сервере, имели другой формат, и не могут быть распознаны."], "The port number is invalid.": ["Недействительный порт."], "Failed to start remote query on a worker.": ["Не удалось запустить удаленный запрос на сервере."], "The database was deleted.": ["База данных была удалена."], "Custom SQL fields cannot contain sub-queries.": ["Пользовательские поля SQL не могут содержать подзапросы."], "The submitted payload failed validation.": ["отправленная полезная нагрузка не удалась."], "Invalid certificate": ["Неверный сертификат"], "The schema of the submitted payload is invalid.": ["Схема представленной полезной нагрузки недействительна."], "Error parsing": ["Ошибка диапазона"], " near '%(highlight)s'": [" почти ‘%(highlight)s'"], " at line %(line)d": [" в линии %(line)d"], ":%(column)d": [":%(column)d"], "Unsafe return type for function %(func)s: %(value_type)s": ["Небезопасный возвращаемый тип для функции %(func)s: %(value_type)s"], "Unsupported return value for method %(name)s": ["Неподдерживаемое значение для метода %(name)s"], "Unsafe template value for key %(key)s: %(value_type)s": ["Небезопасное значение шаблона для ключа %(key)s: %(value_type)s"], "Unsupported template value for key %(key)s": ["Неподдерживаемое значение шаблона для ключа %(key)s"], "Please specify the Dataset ID for the ``%(name)s`` metric in the Jinja macro.": ["Укажите идентификатор Датасета для метрики ``%(name)s`` в макросе Jinja."], "Metric ``%(metric_name)s`` not found in %(dataset_name)s.": ["Метрика `` %(metric_name) s`` не найдена в %(dataset_name)s."], "Viz is missing a datasource": ["У визуализации отсутствует источник данных"], "Applied rolling window did not return any data. Please make sure the source query satisfies the minimum periods defined in the rolling window.": ["Применяемое окно проката не вернуло никаких данных. Применное скользязее окно не вернуло данных. Убедитесь, что исходный запрос удовлетворяет минимальному количеству периодов скользящего окна."], "From date cannot be larger than to date": ["Дата начала не может быть позже даты конца"], "Cached value not found": ["Кэшированное значение не найдено"], "Columns missing in datasource: %(invalid_columns)s": ["Столбцы отсутствуют в источнике данных: %(invalid_columns)s"], "Time Table View": ["Предпросмотр временной таблицы"], "Pick at least one metric": ["Выберите хотя бы одну меру"], "When using 'Group By' you are limited to use a single metric": ["При использовании 'GROUP BY' вы ограничены использованием одной меры"], "Calendar Heatmap": ["Календарная тепловая карта"], "Bubble Chart": ["Пузырьковый график"], "Please use 3 different metric labels": ["Пожалуйста, используйте 3 разных названия метрики"], "Pick a metric for x, y and size": ["Выберите меру для x, y и размера"], "Bullet Chart": ["Диаграмма-шкала"], "Pick a metric to display": ["Выберите меру для отображения"], "Time Series - Line Chart": ["Линейная диаграмма (временные ряды)"], "An enclosed time range (both start and end) must be specified when using a Time Comparison.": ["При условии использования временного сравнения должен быть указан закрытый временной диапазон.При использовании сравнения времени необходимо указать закрытый временной интервал (как начало, так и конец)."], "Time Series - Bar Chart": ["Столбчатая диаграмма (временные ряды)"], "Time Series - Period Pivot": ["Парный t-test (временные ряды)"], "Time Series - Percent Change": ["Периодическая сводка (временные ряды)"], "Time Series - Stacked": ["Диаграмма с накоплением (временные ряды)"], "Histogram": ["Гистограмма"], "Must have at least one numeric column specified": ["Должен быть указан хотя бы один числовой столбец"], "Distribution - Bar Chart": ["Распределение - Столбчатая диаграмма"], "Can't have overlap between Series and Breakdowns": ["Нельзя иметь пересечение между Сериями и Разбивками"], "Pick at least one field for [Series]": ["Выберите хотя бы одно поле для [Серии]"], "Sankey": ["Санкей"], "Pick exactly 2 columns as [Source / Target]": ["Выберите ровно 2 столбца как [Источник / Цель]"], "There's a loop in your Sankey, please provide a tree. Here's a faulty link: {}": ["В вашем Санки есть петля, пожалуйста, предоставьте дерево. В вашем Санкее обнаружен цикл, пожалуйста, предоставьте дерево. Вот неправильная связь: {}"], "Directed Force Layout": ["Ориентированная сила размещения"], "Country Map": ["Карта Стран"], "World Map": ["Карта Мира"], "Parallel Coordinates": ["Параллельные координаты"], "Heatmap": ["Тепловая карта"], "Horizon Charts": ["Horizon Charts"], "Mapbox": ["Mapbox"], "[Longitude] and [Latitude] must be set": ["[Долгота] и [Широта] должны быть заданы"], "Must have a [Group By] column to have 'count' as the [Label]": ["Должен быть столбец [Группировать по], чтобы использовать 'количество' как [Метку]"], "Choice of [Label] must be present in [Group By]": ["[Метка] должна присутствовать в [Группировать по]"], "Choice of [Point Radius] must be present in [Group By]": ["[Радиус точки] должен присутствовать в [Группировать по]"], "[Longitude] and [Latitude] columns must be present in [Group By]": ["Столбцы [Долгота] и [Широта] должны присутствовать в [Группировать по]"], "Deck.gl - Multiple Layers": ["Deck.gl - Multiple Layers"], "Bad spatial key": ["Неподходящий пространственный ключ"], "Invalid spatial point encountered: %(latlong)s": ["Обнаружен недопустимый пространственный пункт: %(latlong)s"], "Encountered invalid NULL spatial entry,                                        please consider filtering those out": ["Встретились недопустимые пространственные записи NULL,                                         пожалуйста, рассмотрите возможность фильтрации таких записей"], "Deck.gl - Scatter plot": ["Deck.gl - <PERSON><PERSON><PERSON> plot"], "Deck.gl - Screen Grid": ["Deck.gl - Screen Grid"], "Deck.gl - 3D Grid": ["Deck.gl - 3D Grid"], "Deck.gl - Paths": ["Deck.gl - Paths"], "Deck.gl - Polygon": ["Deck.gl - Polygon"], "Deck.gl - 3D HEX": ["Deck.gl - 3D HEX"], "Deck.gl - Heatmap": ["Deck.gl - Heatmap"], "Deck.gl - Contour": ["Deck.gl - Contour"], "Deck.gl - GeoJSON": ["Deck.gl - GeoJSON"], "Deck.gl - Arc": ["Deck.gl - Arc"], "Event flow": ["Event flow"], "Time Series - Paired t-test": ["Парный t-test (временные ряды)"], "Time Series - Nightingale Rose Chart": ["Диаграмма Найтингейл (временные ряды)"], "Partition Diagram": ["Partition Diagram"], "Please choose at least one groupby": ["Выберите хотя бы один параметр группировки"], "Invalid advanced data type: %(advanced_data_type)s": ["Невалидный расширенный тип данных: %(advanced_data_type)s"], "Deleted %(num)d annotation layer": ["Удалален %(num)d слой аннотаций", "Удалалены %(num)d слоя аннотаций", "Удалалено %(num)d слоев аннотаций"], "All Text": ["Весь текст"], "Deleted %(num)d annotation": ["Удалалена %(num)d аннотация", "Удалалены %(num)d аннотации", "Удалалено %(num)d аннотаций"], "Deleted %(num)d chart": ["Удален %(num)d график", "Удалены %(num)d графика", "Удалено %(num)d графиков"], "Is certified": ["Одобрено"], "Has created by": ["Создан(а)"], "Created by me": ["Создано мной"], "Owned Created or Favored": ["Принадлежащий созданный или любимый"], "`confidence_interval` must be between 0 and 1 (exclusive)": ["`confidence_interval` должен быть между 0 и 1 (не включая концы)"], "lower percentile must be greater than 0 and less than 100. Must be lower than upper percentile.": ["более низкий процентиль должен быть больше 0 и менее 100. должен быть ниже, чем в верхнем процентиле.Нижний процентиль должен быть больше 0 и меньше 100. Должен быть ниже верхнего процентиля"], "upper percentile must be greater than 0 and less than 100. Must be higher than lower percentile.": ["Верхний процентиль должен быть превышает 0 и менее 100. должен быть выше, чем более низкий процентиль.верхний процентиль должен быть больше 0 и меньше 100. Должен быть выше нижнего процентиля."], "`width` must be greater or equal to 0": ["`width` должeн быть больше или равна 0"], "`row_limit` must be greater than or equal to 0": ["`row_limit` должно быть больше или равно 0"], "`row_offset` must be greater than or equal to 0": ["`row_offset` должно быть больше или равно 0"], "orderby column must be populated": ["Порядок столбца должен быть заполнен"], "Chart has no query context saved. Please save the chart again.": ["На графике не сохранен контекст запроса. Пожалуйста, сохраните диаграмму еще раз."], "Request is incorrect: %(error)s": ["Неверный запрос: %(error)s"], "Request is not JSON": ["Запрос не является JSON"], "Empty query result": ["Пустой ответ запроса"], "Owners are invalid": ["Неверный список владельцев"], "Some roles do not exist": ["Некоторые роли не существуют"], "Datasource type is invalid": ["Тип источниках данных неверный"], "Datasource does not exist": ["Источник данных не существует"], "Query does not exist": ["Запрос не существует"], "Annotation layer parameters are invalid.": ["Параметры слоя аннотаций недействительны"], "Annotation layer could not be created.": ["Не удалось создать слой аннотации."], "Annotation layer could not be updated.": ["Не удалось обновить слой аннотации."], "Annotation layer not found.": ["Слой аннотации не найден."], "Annotation layers could not be deleted.": ["Аннотация нельзя удалить."], "Annotation layer has associated annotations.": ["Слои аннотаций имеет связанные аннотации."], "Name must be unique": ["Имя должно быть уникальным"], "End date must be after start date": ["Дата окончания должна быть позже даты начала"], "Short description must be unique for this layer": ["Содержимое аннотации должно быть уникальным внутри слоя"], "Annotation not found.": ["Аннотация не найдена."], "Annotation parameters are invalid.": ["Параметры аннотации недействительны."], "Annotation could not be created.": ["Не удалось создать аннотацию"], "Annotation could not be updated.": ["Не удалось обновить аннотацию"], "Annotations could not be deleted.": ["Не удалось удалить аннотации."], "There are associated alerts or reports: %(report_names)s": ["Существуют связанные оповещения или отчеты: %(report_names)s"], "Time string is ambiguous. Please specify [%(human_readable)s ago] or [%(human_readable)s later].": ["Строка времени неоднозначна. Временная строка неоднозначна. Пожалуйста, укажите [%(human_readable)s до] или [%(human_readable)s после]."], "Cannot parse time string [%(human_readable)s]": ["Не удается разобрать временную строку [%(human_readable)s]"], "Time delta is ambiguous. Please specify [%(human_readable)s ago] or [%(human_readable)s later].": ["Время Дельта неоднозначна. Неоднозначный временной сдвиг. Пожалуйста, укажите [%(human_readable)s до] или [%(human_readable)s после]."], "Database does not exist": ["База данных не существует"], "Dashboards do not exist": ["Дашборды не существуют"], "Datasource type is required when datasource_id is given": ["Тип данных требуется, когда дается dataSource_idТип источника данных обязателен, когда дан идентификатор источника данных (datasource_id)"], "Chart parameters are invalid.": ["Параметры графика недопустимы."], "Chart could not be created.": ["Не удалось создать график"], "Chart could not be updated.": ["Не удалось обновить график"], "Charts could not be deleted.": ["Не удалось удалить графики."], "There are associated alerts or reports": ["Есть связанные оповещения или отчеты"], "You don't have access to this chart.": ["Недостаточно прав для доступа к этому графику."], "Changing this chart is forbidden": ["Запрещено изменять этот график"], "Import chart failed for an unknown reason": ["Не удалось импортировать график по неизвестной причине"], "Changing one or more of these dashboards is forbidden": ["Изменение одной или нескольких из этих мониторинга запрещено"], "Chart not found": ["График не найден"], "Error faving chart": ["Ошибка блюда"], "Error unfaving chart": ["Ошибка удаления графика из избранного"], "Error: %(error)s": ["Ошибка: %(error)s"], "CSS templates could not be deleted.": ["Шаблоны CSS не могут быть удалены."], "CSS template not found.": ["CSS шаблон не найден."], "Must be unique": ["Должно быть уникальным"], "Dashboard parameters are invalid.": ["Неверные параметры дашборда."], "Dashboards could not be created.": ["Монитонные панели не могут быть созданы."], "Dashboard could not be updated.": ["Не удалось обновить дашборд"], "Dashboard could not be deleted.": ["Не удалось удалить дашборд"], "Embedded dashboard could not be deleted.": ["Встроенный дашборд не может быть удален."], "Changing this Dashboard is forbidden": ["Запрещено изменять этот дашборд"], "Import dashboard failed for an unknown reason": ["Не удалось импортировать дашборд по неизвестной причине"], "You don't have access to this dashboard.": ["Недостаточно прав для доступа к этому дашборду."], "Dashboard cannot be copied due to invalid parameters.": ["Панель панели не может быть скопирована из -за неверных параметров."], "Dashboard cannot be favorited.": ["Панель панели не может быть влюблена."], "Dashboard cannot be unfavorited.": ["Панель панели не может быть не поддержана."], "You don't have access to this embedded dashboard config.": ["У вас нет прав на редактирование этого встраиваемого дашборда."], "No data in file": ["В файле нет данных"], "Database parameters are invalid.": ["Параметры базы данных недействительны."], "A database with the same name already exists.": ["База данных с таким же именем уже существует"], "Field is required": ["Поле обязательно к заполнению"], "Field cannot be decoded by JSON. %(json_error)s": ["Поле не может быть декодировано с помощью JSON. %(json_error)s"], "The metadata_params in Extra field is not configured correctly. The key %{key}s is invalid.": ["Metadata_Params в дополнительном поле настроено неправильно. Ключ %{key}s некорректный."], "Database not found.": ["База данных не найдена."], "Database schema is not allowed for csv uploads.": ["Схема базы данных не допускается для загрузки CSV."], "Database type does not support file uploads.": ["Тип базы данных не поддерживает загрузки файлов."], "Database upload file failed": ["Файл загрузки базы данных не удался"], "Database upload file failed, while saving metadata": ["Файл загрузки базы данных не удался, при сохранении метаданных"], "Database could not be created.": ["Не удалось создать базу данных."], "Database could not be updated.": ["Не удалось обновить базу данных."], "Connection failed, please check your connection settings": ["Сбой подключения, пожалуйста, проверьте настройки вашего подключения"], "Cannot delete a database that has datasets attached": ["Невозможно удалить базу данных с подключенными датасетами"], "Database could not be deleted.": ["Не удалось удалить базу данных."], "Stopped an unsafe database connection": ["остановил небезопасное соединение базы данных"], "Could not load database driver": ["Не удалось загрузить драйвер базы данных"], "Unexpected error occurred, please check your logs for details": ["Произошла неожиданная ошибка, пожалуйста, проверьте свои журналы для получения подробной информацииВозникла неожиданная ошибка, пожалуйста, проверьте историю действий для уточнения деталей"], "no SQL validator is configured": ["не настроен ни один SQL валидатор"], "No validator found (configured for the engine)": ["Не найден валидатор (сконфигурированный для драйвера)"], "Was unable to check your query": ["Не удалось проверить запрос"], "An unexpected error occurred": ["Произошла неожиданная ошибка"], "Import database failed for an unknown reason": ["Не удалось импортировать базу данных по неизвестной причине"], "Could not load database driver: {}": ["Не удалось загрузить драйвер базы данных: {}"], "SSH Tunnel could not be deleted.": ["Не удалось удалить SSH туннель."], "SSH Tunnel not found.": ["SSH туннель не найден."], "SSH Tunnel parameters are invalid.": ["Параметры SSH туннеля недопустимы."], "A database port is required when connecting via SSH Tunnel.": ["При подключении через туннель SSH требуется порт базы данных."], "SSH Tunnel could not be updated.": ["Не удалось обновить SSH туннель."], "Creating SSH Tunnel failed for an unknown reason": ["Не удалось создать SSH туннель по неизвестной причине"], "SSH Tunneling is not enabled": ["туннелирование SSH не включено"], "Must provide credentials for the SSH Tunnel": ["Должен предоставить учетные данные для туннеля SSH"], "Cannot have multiple credentials for the SSH Tunnel": ["не может иметь несколько учетных данных для туннеля SSH"], "Table already exists. You can change your 'if table already exists' strategy to append or replace or provide a different Table Name to use.": ["Таблица уже существует. Вы можете изменить стратегию «если таблица уже существует» на добавление или замену или указать другое имя таблицы для использования."], "Parsing error: %(error)s": ["Ошибка Парсинга: %(error)s"], "Error reading Columnar file": ["Ошибка загрузки Columnar"], "Unexpected no file extension found": ["Неожиданно не найдено расширение файла"], "Not a valid ZIP file": ["Не действительный zip -файл"], "ZIP file contains multiple file types": ["zip -файл содержит несколько типов файлов"], "Error reading CSV file": ["Ошибка чтения файла CSV"], "Error reading Excel file": ["Ошибка чтения файла Excel"], "Excel file format cannot be determined": ["Формат файла Excel не может быть определен"], "Dataset %(table)s already exists": ["Дотаяет %(table)s уже существует"], "Database not allowed to change": ["База данных недоступна для изменений"], "One or more columns do not exist": ["Один или несколько столбцов не существуют"], "One or more columns are duplicated": ["Один или несколько столбцов дублируются"], "One or more columns already exist": ["Один или несколько столбцов уже существуют"], "One or more metrics do not exist": ["Одна или несколько мер не существуют"], "One or more metrics are duplicated": ["Одна или несколько мер дублируются"], "One or more metrics already exist": ["Одна или несколько мер уже существуют"], "Table [%(table)s] could not be found, please double check your database connection, schema, and table name": ["Таблица [%(table)s] не может быть найдена, пожалуйста, дважды проверьте соединение базы данных, схема и имя таблицы"], "Dataset does not exist": ["Датасет не существует"], "Dataset parameters are invalid.": ["Параметры датасета неверны."], "Dataset could not be created.": ["Не удалось создать датасет"], "Dataset could not be updated.": ["Не удалось обновить датасет"], "Datasets could not be deleted.": ["Наборы данных не могут быть удалены."], "Samples for dataset could not be retrieved.": ["Не удалось получить примеры записей датасета."], "Changing this dataset is forbidden": ["Запрещено изменять этот датасет"], "Import dataset failed for an unknown reason": ["Не удалось импортировать датасет по неизвестной причине"], "You don't have access to this dataset.": ["Недостаточно прав для доступа к этому датасету."], "Dataset could not be duplicated.": ["Дата<PERSON>ет не может быть дублирован."], "Data URI is not allowed.": ["Data URI не допускается."], "The provided table was not found in the provided database": ["Предоставленная таблица не была найдена в предоставленной базе данных"], "Dataset column not found.": ["Столбец датасета не найден."], "Dataset column delete failed.": ["Не удалось удалить столбец датасета"], "Changing this dataset is forbidden.": ["Запрещено изменять данный датасет."], "Dataset metric not found.": ["Мера датасета не найдена."], "Dataset metric delete failed.": ["Не удалось удалить меру датасета."], "Form data not found in cache, reverting to chart metadata.": ["Данные формы не найдены в кэше, возвращение к метаданным графика."], "Form data not found in cache, reverting to dataset metadata.": ["Данные формы не найдены в кэше, возвращение к метаданным датасета."], "[Missing Dataset]": ["[отсутствующий датасет]"], "Onboarding parameters are invalid.": ["Параметры по адаптации недействительны."], "Onboarding could not be updated.": ["Встроение не может быть обновлено."], "Changing this Onboarding is forbidden": ["Изменение этой адаптации запрещено"], "You don't have access to this Onboarding.": ["У вас нет доступа к этой адаптации."], "Saved queries could not be deleted.": ["Не удалось удалить сохраненные запросы."], "Saved query not found.": ["Сохраненный запрос не найден."], "Import saved query failed for an unknown reason.": ["Не удалось импортировать сохраненный запрос по неизвестной причине"], "Saved query parameters are invalid.": ["Сохраненные параметры запроса недопустимы."], "Alert query returned more than one row. %(num_rows)s rows returned": ["Запрос оповещения вернул более одного строки.  %(num_rows)s строк возвращено"], "Alert query returned more than one column. %(num_cols)s columns returned": ["Запрос оповещения вернул более одного столбца. %(num_cols)s столбцов возвращено"], "An error occurred when running alert query": ["Произошла ошибка при запуске запроса оповещения"], "Invalid tab ids: %s(tab_ids)": ["Неверные идентификаторы вкладок: %s(tab_ids)"], "Dashboard does not exist": ["Дашборд не существует"], "Chart does not exist": ["График не существует"], "Database is required for alerts": ["Для оповещений требуется база данных"], "Type is required": ["Поле обязательно"], "Choose a chart or dashboard not both": ["Выберите график или дашборд, не обоих"], "Must choose either a chart or a dashboard": ["Выберите график или дашборд"], "%(report_type)s schedule frequency exceeding limit. Please configure a schedule with a minimum interval of %(minimum_interval)d minutes per execution.": ["%(report_type)s частота превышения графика. Пожалуйста, настройте расписание с минимальным интервалом в %(minimum_interval)d минут на выполнение."], "Please save your chart first, then try creating a new email report.": ["Пожалуйста, сначала сохраните график перед тем, как создавать новую рассылку."], "Please save your dashboard first, then try creating a new email report.": ["Пожалуйста, сначала сохраните дашборд перед тем, как создавать новую рассылку."], "Report Schedule parameters are invalid.": ["Параметры расписания отчета неверны."], "Report Schedule could not be created.": ["Невозможно удалить расписание отчета."], "Report Schedule could not be updated.": ["Невозможно обновить расписание отчета"], "Report Schedule not found.": ["Расписание отчета не найдено"], "Report Schedule delete failed.": ["Ошибка при удалении расписания отчета."], "Report Schedule log prune failed.": ["отчет о графике."], "Report Schedule execution failed when generating a screenshot.": ["Возникла ошибка при создании скриншота для отправки отчета"], "Report Schedule execution failed when generating a pdf.": ["Выполнение расписания отчетов не удалось при генерации PDF."], "Report Schedule execution failed when generating a csv.": ["Возникла ошибка при создании csv для отправки отчета"], "Report Schedule execution failed when generating a dataframe.": ["Возникла ошибка при создании датафрейма для отправки отчета"], "Report Schedule execution got an unexpected error.": ["Возникла неожиданная ошибка при отправке отчета"], "Report Schedule is still working, refusing to re-compute.": ["Планировщик отчетов все еще работает, не имея возможности отправить отчет"], "Report Schedule reached a working timeout.": ["Расписание отчетов достигло рабочего таймаута."], "A report named \"%(name)s\" already exists": ["Рассылка с именем \"%(name)s\" уже существует"], "An alert named \"%(name)s\" already exists": ["Оповещение с именем \"%(name)s\" уже существует"], "Resource already has an attached report.": ["Для этого компонента уже создан отчет."], "Alert query returned more than one row.": ["Запрос оповещения вернул больше, чем одну строку."], "Alert validator config error.": ["Неверная конфигурация валидатора оповещений."], "Alert query returned more than one column.": ["Запрос оповещения вернул больше, чем один столбец."], "Alert query returned a non-number value.": ["Запрос оповещения вернул нечисловое значение."], "Alert found an error while executing a query.": ["Возникла ошибка при выполнении запроса для оповещения."], "A timeout occurred while executing the query.": ["Вышло время исполнения запроса."], "A timeout occurred while taking a screenshot.": ["Вышло время создания скриншота."], "A timeout occurred while generating a csv.": ["Вышло время создания CSV файла."], "A timeout occurred while generating a dataframe.": ["Вышло время создания датафрейма."], "Alert fired during grace period.": ["Оповещение сработало во время перерыва"], "Alert ended grace period.": ["У оповещения закончился перерыв."], "Alert on grace period": ["Оповещение во время перерыва"], "Report Schedule state not found": ["Состояние расписания отчета не найдено"], "Report schedule system error": ["Возникла ошибка расписания отчета на стороне системы"], "Report schedule client error": ["Возникла ошибка расписания отчета на стороне клиента"], "Report schedule unexpected error": ["Неожиданная ошибка расписания отчета"], "Changing this report is forbidden": ["Запрещено изменять эту рассылку"], "An error occurred while pruning logs ": ["Произошла ошибка при удалении журналов "], "RLS Rule not found.": ["Правило RLS не найдено."], "RLS rules could not be deleted.": ["Правила RLS не могут быть удалены."], "Statement parameters are invalid.": ["Параметры оператора недействительны."], "Statements could not be created.": ["Заявления не могут быть созданы."], "Statement could not be updated.": ["Заявление не может быть обновлено."], "Changing this Statement is forbidden": ["Изменение этого утверждения запрещено"], "You don't have access to this Statement.": ["У вас нет доступа к этому утверждению."], "Tag parameters are invalid.": ["Параметры тега недействительны."], "Tag could not be created.": ["Тэг не может быть создан."], "Tag could not be updated.": ["Тег не может быть обновлен."], "Tag could not be deleted.": ["Тэг не может быть удален."], "Tagged Object could not be deleted.": ["Tagged Object не может быть удален."], "Team parameters are invalid.": ["Параметры команды недействительны."], "Teams could not be created.": ["Команды не могли быть созданы."], "Team could not be updated.": ["Команда не может быть обновлена."], "Team could not be deleted.": ["Команда не может быть удалена."], "Changing this Team is forbidden": ["Изменение этой команды запрещено"], "You don't have access to this Team.": ["У вас нет доступа к этой команде."], "An error occurred while creating the value.": ["Произошла ошибка при создании значения"], "An error occurred while accessing the value.": ["Произошла ошибка при доступе к значению"], "An error occurred while deleting the value.": ["Произошла ошибка при удалении значения"], "An error occurred while updating the value.": ["Произошла ошибка при обновлении значения"], "You don't have permission to modify the value.": ["Недостаточно прав для редактирования этого значения."], "Resource was not found.": ["Источник не был найден."], "Invalid result type: %(result_type)s": ["Недопустимый тип ответа: %(result_type)s"], "Columns missing in dataset: %(invalid_columns)s": ["Столбцы отсутствуют в датасете: %(invalid_columns)s"], "Time Grain must be specified when using Time Shift.": ["Временное зерно должно быть указано при использовании временного смены."], "The chart does not exist": ["График не существует"], "The chart datasource does not exist": ["Источник данных графика не существует"], "The chart query context does not exist": ["Контекст запроса графика не существует"], "Duplicate column/metric labels: %(labels)s. Please make sure all columns and metrics have a unique label.": ["Дубликат наименования столбца/метрики: %(labels)s. Пожалуйста, убедитесь, что все столбцы и меры имеют уникальную метку."], "The following entries in `series_columns` are missing in `columns`: %(columns)s. ": ["В `columns` отсутствуют следующие записи в `series_columns`: %(columns)s. "], "`operation` property of post processing object undefined": ["`operation` свойство объекта постобработки не определено"], "Unsupported post processing operation: %(operation)s": ["Неподдерживаемая операция постобработки: %(operation)s"], "Error in jinja expression in RLS filters: %(msg)s": ["Ошибка в jinja выражении в RLS фильтрах: %(msg)s"], "Error in jinja expression in fetch values predicate: %(msg)s": ["Ошибка в jinja выражении в предикате выборки значений: %(msg)s"], "Virtual dataset query must be read-only": ["Запрос виртуального датасета должен быть доступен только для чтения"], "Metric '%(metric)s' does not exist": ["Мера '%(metric)s' не существует"], "Db engine did not return all queried columns": ["Драйвер базы данных вернул не все запрошенные столбцы"], "Virtual dataset query cannot be empty": ["Запрос виртуального датасета не может быть пустым"], "Only `SELECT` statements are allowed": ["Доступны только SELECT запросы"], "Only single queries supported": ["Поддерживаются только одиночные запросы"], "Columns": ["Столбцы"], "Show Column": ["Показать столбец"], "Add Column": ["Добавить столбец"], "Edit Column": ["Редактировать столбец"], "Whether to make this column available as a [Time Granularity] option, column has to be DATETIME or DATETIME-like": ["Чтобы сделать этот столбец доступным в качестве опции [Time Granularity], столбец должен быть DATETIME или DATETIME-like"], "Whether this column is exposed in the `Filters` section of the explore view.": ["Разоблачен ли этот столбец в разделе «Фильтры» представления «Исследование»."], "The data type that was inferred by the database. It may be necessary to input a type manually for expression-defined columns in some cases. In most case users should not need to alter this.": ["Тип данных, который был выведен в базе данных."], "Column": ["Столбец"], "Verbose Name": ["Удобочитаемое имя"], "Description": ["Описание"], "Groupable": ["Группируемый"], "Filterable": ["Фильтруемый"], "Table": ["Таблица"], "Expression": ["Выражение"], "Is temporal": ["Содержит дату/время"], "Datetime Format": ["Формат даты и времени"], "Type": ["Тип"], "Business Data Type": ["Тип данных бизнеса"], "Invalid date/timestamp format": ["Недопустимый формат дата/время"], "Metrics": ["Меры"], "Show Metric": ["Показатель меру"], "Add Metric": ["Добавить меру"], "Edit Metric": ["Редактировать меру"], "Metric": ["Мера"], "SQL Expression": ["SQL выражение"], "D3 Format": ["Формат даты/времени"], "Extra": ["Дополнительные параметры"], "Warning Message": ["Предупреждение"], "Tables": ["Таблицы"], "Show Table": ["Показать таблицу"], "Import a table definition": ["импортируйте определение таблицы"], "Edit Table": ["Редактировать таблицу"], "The list of charts associated with this table. By altering this datasource, you may change how these associated charts behave. Also note that charts need to point to a datasource, so this form will fail at saving if removing charts from a datasource. If you want to change the datasource for a chart, overwrite the chart from the 'explore view'": ["Список графиков, связанных с этой таблицей. Изменяя этот источник данных, вы можете изменить поведение этих связанных графиков. Также обратите внимание, что графики должны указывать на источник данных, поэтому эта форма не сможет сохраниться, если удалить графики из источника данных. Если вы хотите изменить источник данных для графика, перезапишите график из 'Исследовать вид'"], "Timezone offset (in hours) for this datasource": ["Смещение часового пояса (в часах) для этого источника данных"], "Name of the table that exists in the source database": ["Имя таблицы, которая существует в базе данных"], "Schema, as used only in some databases like Postgres, Redshift and DB2": ["Схема, используемая только в некоторых базах данных, таких как Postgres, Redshift и DB2"], "This fields acts a Superset view, meaning that Superset will run a query against this string as a subquery.": ["Это поля действует в суперсет, что означает, что Superset будет запускать запрос против этой строки в качестве подразделения."], "Predicate applied when fetching distinct value to populate the filter control component. Supports jinja template syntax. Applies only when `Enable Filter Select` is on.": ["Предикат, применяемый при получении различного значения для заполнения компонента управления фильтром."], "Redirects to this endpoint when clicking on the table from the table list": ["Перенаправление в эту конечную точку при нажатии на таблицу из списка таблиц"], "Whether to populate the filter's dropdown in the explore view's filter section with a list of distinct values fetched from the backend on the fly": ["Заполнить ли раскрывающийся список фильтра в разделе фильтра Explore View список различных значений, извлеченных из бэкэнд на лету"], "Whether the table was generated by the 'Visualize' flow in SQL Lab": ["Была ли таблица сгенерирована путем «визуализации» в SQL Lab"], "A set of parameters that become available in the query using Jinja templating syntax": ["Набор парам<PERSON><PERSON><PERSON><PERSON>, которые доступны в запросе через шаблонизацию Jinja."], "Duration (in seconds) of the caching timeout for this table. A timeout of 0 indicates that the cache never expires. Note this defaults to the database timeout if undefined.": ["Продолжительность (в секундах) жизни кэша для этой таблицы. Обратите внимание, что если значение не задано, применяется значение по умолчанию."], "Allow column names to be changed to case insensitive format, if supported (e.g. Oracle, Snowflake).": ["Разрешить изменять имена столбцов на нечувствительный формат, если поддерживается (например, Oracle, Snowflake)."], "Datasets can have a main temporal column (main_dttm_col), but can also have secondary time columns. When this attribute is true, whenever the secondary columns are filtered, the same filter is applied to the main datetime column.": ["Наборы данных могут иметь основной временный столбец (main_dttm_col), но также могут иметь вторичные временные столбцы."], "Associated Charts": ["Связанные графики"], "Changed By": ["Кем изменено"], "Database": ["База данных"], "Last Changed": ["Дата изменения"], "Enable Filter Select": ["Включите выбор фильтра"], "Schema": ["Схема"], "Default Endpoint": ["Эндпоинт по умолчанию"], "Offset": ["Смещение"], "Cache Timeout": ["Время жизни кэша"], "Table Name": ["Имя таблицы"], "Fetch Values Predicate": ["Значения получения предиката"], "Owners": ["Владельцы"], "Main Datetime Column": ["Основной столбец с временем"], "SQL Lab View": ["SQL Lab View"], "Template parameters": ["Параметры шаблона"], "Modified": ["Изменено"], "The table was created. As part of this two-phase configuration process, you should now click the edit button by the new table to configure it.": ["Таблица была создана. В рамках этого двухфазного процесса настройки теперь следует нажать кнопку редактирования новой таблицы, чтобы настроить ее."], "Deleted %(num)d css template": ["Удален %(num)d CSS шаблон", "Удалены %(num)d CSS шаблона", "Удалено %(num)d CSS шаблонов"], "Dataset schema is invalid, caused by: %(error)s": ["Схема датасета невалидна, причина: %(error)s"], "Tab schema is invalid, caused by: %(error)s": ["Схема вкладок недействительна, вызванная: %(error)s"], "Deleted %(num)d dashboard": ["Удален %(num)d дашборд", "Удалены %(num)d дашборда", "Удалено %(num)d дашбордов"], "Title or Slug": ["Название"], "Role": ["Роль"], "Invalid state.": ["неверное состояние."], "Table name undefined": ["Имя таблицы не определено"], "Upload Enabled": ["Загрузка включена"], "Invalid connection string, a valid string usually follows: backend+driver://user:password@database-host/database-name": ["Недопустимая строка подключения, правильная строка обычно следует: Backend Driver: // Пользователь: Password@Database-Host/Database-NameНедопустимая строка для подключения, валидная строка соответствует шаблону: драйвер://имя-пользователя:пароль@хост/имя-базы-данных"], "Field cannot be decoded by JSON. %(msg)s": ["Поле не может быть декодировано с помощью JSON. %(msg)s"], "The metadata_params in Extra field is not configured correctly. The key %(key)s is invalid.": ["Metadata_params в дополнительном поле настроено неправильно. Ключ %(key)s некорректный."], "An engine must be specified when passing individual parameters to a database.": ["Движок должен быть указан при передаче индивидуальных параметров к базе."], "Engine spec \"InvalidEngine\" does not support being configured via individual parameters.": ["Спецификация двигателя \\ \"Invalidengine \" не поддерживает настройку с помощью отдельных параметров."], "File extension is not allowed.": ["Расширение файла не допускается."], "File size exceeds the maximum allowed size.": ["Размер файла превышает максимально допустимый размер."], "Deleted %(num)d dataset": ["Удален %(num)d датасет", "Удалены %(num)d датасета", "Удалено %(num)d датасетов"], "Null or Empty": ["Null или <PERSON>то"], "Unknown Presto Error": ["Неизвестная ошибка Presto"], "Samples for datasource could not be retrieved.": ["Не удалось получить примеры записей для источника данных."], "Changing this datasource is forbidden": ["Запрещено изменять этот источник данных"], "An error occurred while parsing the key.": ["Произошла ошибка при парсинге ключа."], "An error occurred while upserting the value.": ["Произошла ошибка при вставке значения."], "Unable to encode value": ["Невозможно кодировать значение"], "Unable to decode value": ["Невозможно декодировать значение"], "Invalid permalink key": ["Недействительный ключ для постоянной ссылки"], "Error while rendering virtual dataset query: %(msg)s": ["Произошла ошибка при выполнении запроса виртуального датасета: %(msg)s"], "Virtual dataset query cannot consist of multiple statements": ["Запрос виртуального датасета не может содержать несколько запросов"], "Datetime column not provided as part table configuration and is required by this type of chart": ["Столбец DateTime не предоставлен в качестве конфигурации таблицы деталей и требуется этим типом графика"], "Empty query?": ["Пустой запрос?"], "Unknown column used in orderby: %(col)s": ["Неизвестный столбец использован для упорядочивания: %(col)s"], "Time column \"%(col)s\" does not exist in dataset": ["Столбец формата дата/время \"%(col)s\" не существует в датасете"], "error_message": ["error_message"], "Filter value list cannot be empty": ["Список для фильтрации не может быть пуст"], "Must specify a value for filters with comparison operators": ["Необходимо указать значение для фильтров с операторами сравнения"], "Invalid filter operation type: %(op)s": ["Неверный тип работы фильтра: %(op)s"], "Error in jinja expression in WHERE clause: %(msg)s": ["Ошибка в jinja выражении в операторе WHERE: %(msg)s"], "Error in jinja expression in HAVING clause: %(msg)s": ["Ошибка в jinja выражении в операторе HAVING: %(msg)s"], "Database does not support subqueries": ["База данных не поддерживает подзапросы"], "Deleted %(num)d saved query": ["Удален %(num)d сохраненный запрос", "Удалены %(num)d сохраненных запроса", "Удалено %(num)d сохраненных запросов"], "Deleted %(num)d report schedule": ["Удалено %(num)d расписание рассылок", "Удалены %(num)d расписания рассылок", "Удалено %(num)d расписаний рассылок"], "Value must be greater than 0": ["Значение должно быть больше 0"], "Custom width of the screenshot in pixels": ["Пользовательская ширина скриншота в пикселях"], "Screenshot width must be between %(min)spx and %(max)spx": ["Ширина скриншота должна быть между %(min)spx и %(max)spx"], "Value must be 0 or greater": ["Значение должно быть 0 или выше"], "Deleted %(num)d rules": ["Удалено %(num)d правило", "Удалены %(num)d правила", "Удалено %(num)d правил"], "%(dialect)s cannot be used as a data source for security reasons.": ["%(dialect)s не может использоваться в качестве источника данных по соображениям безопасности."], "ID": ["ID"], "Team": ["Команда"], "Dodo role": ["DODO роль"], "Country": ["Страна"], "User info": ["Данные пользователя"], "Personal Info": ["Персональные данные"], "Audit Info": ["Аудит данные"], "Guest user cannot modify chart payload": ["Гость не может изменить отправляемые данные графика"], "You don't have the rights to alter %(resource)s": ["Недостаточно прав для изменения %(resource)s"], "Failed to execute %(query)s": ["Не удалось выполнить %(query)s"], "The parameter %(parameters)s in your query is undefined.": ["Параметр %(parameters)s в вашем запросе неопределен.", "Следующие параметры неопределены в вашем запросе: %(parameters)s", "Следующие параметры неопределены в вашем запросе: %(parameters)s"], "finished": ["закончил"], "id": ["идентификатор"], "first_name": ["first_Name"], "Tag name is invalid (cannot contain ':')": ["Имя тега недействительно (не может содержать ':')"], "Tag could not be found.": ["Тег не может быть найден."], "Is custom tag": ["Это пользовательский тег"], "Is tagged": ["Помечено"], "Scheduled task executor not found": ["Исполнитель регулярных отчетов не найден"], "Name": ["Имя"], "Slug": ["Читаемый URL"], "External": ["Внешний"], "Record Count": ["Кол-во записей"], "No records found": ["Записи не найдены"], "Filter List": ["Список фильтров"], "Search": ["Поиск"], "Refresh": ["Обновить"], "Import dashboards": ["Импортировать дашборды"], "Import Dashboard(s)": ["Импортировать дашборд(ы)"], "File": ["<PERSON>а<PERSON><PERSON>"], "Choose File": ["Выберите файл"], "Upload": ["Загрузить"], "Use the edit button to change this field": ["Используйте кнопку редактирования для изменения поля"], "Test Connection": ["Тестовое соединение"], "Unable to calculate such a date delta": ["Невозможно рассчитать такую ​​дату дельту"], "Unable to find such a holiday: [%(holiday)s]": ["Не удалось найти такой праздник: [%(holiday)s]"], "DB column %(col_name)s has unknown type: %(value_type)s": ["DB column %(col_name)s имеет неизвестный тип: %(value_type)s"], "percentiles must be a list or tuple with two numeric values, of which the first is lower than the second value": ["Проценли должны быть списком или кортежом с двумя числовыми значениями, из которых первое ниже второго значения"], "`compare_columns` must have the same length as `source_columns`.": ["`compare_columns` должен иметь ту же длину, что и` source_columns`."], "`compare_type` must be `difference`, `percentage` or `ratio`": ["`compare_type` должен быть` differe`, `процент 'или` отношение"], "Column \"%(column)s\" is not numeric or does not exists in the query results.": ["Столбец \"%(column)s\" не является числовым или отсутствует в результатах запроса."], "`rename_columns` must have the same length as `columns` + `time_shift_columns`.": ["`rename_columns` должен иметь ту же длину, что и` columns` `time_shift_columns`."], "Invalid cumulative operator: %(operator)s": ["Недействительный кумулятивный оператор: %(operator)s"], "Invalid geohash string": ["Неверная строка geohash"], "Invalid longitude/latitude": ["Недопустимые долгота/широта"], "Invalid geodetic string": ["Неверная geodetic строка"], "Pivot operation requires at least one index": ["операция поворота требует как минимум одного индекса"], "Pivot operation must include at least one aggregate": ["операция поворота должна включать как минимум один заполнитель"], "`prophet` package not installed": ["`Пакет Пророк не установлен"], "Time grain missing": ["Единица времени отсутствует"], "Unsupported time grain: %(time_grain)s": ["Неподдерживаемая единица времени: %(time_grain)s"], "Periods must be a whole number": ["Периоды должны быть целым числом"], "Confidence interval must be between 0 and 1 (exclusive)": ["Доверительный интервал должен быть между 0 и 1 (не включая концы)"], "DataFrame must include temporal column": ["Датафрейм должен включать временной столбец"], "DataFrame include at least one series": ["DataFrame включает как минимум одну серию"], "Label already exists": ["Метка уже существует"], "Resample operation requires DatetimeIndex": ["Для ресемплирования требуется индекс формата дата/время"], "Resample method should be in ": ["Метод повторного примера должен быть в "], "Undefined window for rolling operation": ["Неопределенное окно для скольжения"], "Window must be > 0": ["Окно должно быть > 0"], "Invalid rolling_type: %(type)s": ["Неверный rolling_type: %(type)s"], "Invalid options for %(rolling_type)s: %(options)s": ["Недопустимые настройки для %(rolling_type)s: %(options)s"], "Referenced columns not available in DataFrame.": ["Столбцы с ссылками недоступны в DataFrame."], "Column referenced by aggregate is undefined: %(column)s": ["Столбец, на который ссылается агрегат, не определен: %(column)s"], "Operator undefined for aggregator: %(name)s": ["Оператор не определен для агрегатора: %(name)s"], "Invalid numpy function: %(operator)s": ["Недопустимая numpy функция: %(operator)s"], "Unexpected time range: %(error)s": ["Неожиданный диапазон времени: %(error)s"], "Is favorite": ["В избранном"], "You don't have the rights to download as csv": ["Недостаточно прав для скачивания в CSV"], "Error: permalink state not found": ["Ошибка: состояние постоянной ссылки не найдено"], "You don't have the rights to alter this chart": ["Недостаточно прав для изменения графика"], "You don't have the rights to create a chart": ["Недостаточно прав для создания графика"], "Explore - %(table)s": ["Исследовать - %(table)s"], "Explore": ["Исследовать"], "Chart [{}] has been saved": ["График [{}] сохранен"], "Chart [{}] has been overwritten": ["График [{}] перезаписан"], "You don't have the rights to alter this dashboard": ["Недостаточно прав для изменения дашборда"], "Chart [{}] was added to dashboard [{}]": ["График [{}] добавлен в дашборд [{}]"], "You don't have the rights to create a dashboard": ["Недостаточно прав для создания дашборда"], "Dashboard [{}] just got created and chart [{}] was added to it": ["Дашборд [{}] был только что создан и график [{}] был добавлен в него"], "permalink state not found": ["состояние постоянной ссылки не найдено"], "A human-friendly name": ["Человекочитаемое имя"], "Used internally to identify the plugin. Should be set to the package name from the pluginʼs package.json": ["Используется внутренне для идентификации плагина."], "A full URL pointing to the location of the built plugin (could be hosted on a CDN for example)": ["Полный URL, указывающий на местоположение плагина (например, ссылка на CDN)"], "Custom Plugins": ["Пользовательские плагины"], "Custom Plugin": ["Пользовательский плагин"], "Add a Plugin": ["Добавить плагин"], "Edit Plugin": ["Редактировать плагин"], "The dataset associated with this chart no longer exists": ["Дата<PERSON><PERSON><PERSON>, связанный с этим графиком, больше не существует"], "Could not determine datasource type": ["Не удалось определить тип источника данных"], "Could not find viz object": ["Не удалось найти объект визуализации"], "Charts": ["Гра<PERSON>и<PERSON>и"], "Show Chart": ["Показать график"], "Add Chart": ["Добавить график"], "Edit Chart": ["Редактировать график"], "These parameters are generated dynamically when clicking the save or overwrite button in the explore view. This JSON object is exposed here for reference and for power users who may want to alter specific parameters.": ["Эти параметры генерируются динамически при нажатии кнопки «Сохранить» или «Перезапись» в представлении «Исследование». Эти параметры генерируются автоматически при нажатии кнопки сохранения. Опытные пользователи могут изменить определенные объекты в формате JSON."], "Duration (in seconds) of the caching timeout for this chart. Note this defaults to the datasource/table timeout if undefined.": ["Продолжительность (в секундах) тайм -аута кэширования для этой таблицы. Продолжительность (в секундах) таймаута кэша для этого графикаОбратите внимание, что если значение не задано, применяется значение источника данных/таблицы."], "Creator": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Dashboards": ["Дашборды"], "Datasource": ["Источник данных"], "Last Modified": ["Дата изменения"], "Parameters": ["Параметры"], "Chart": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "Visualization Type": ["Тип визуализации"], "Show Dashboard": ["Показать дашборд"], "Add Dashboard": ["Добавить дашборд"], "Edit Dashboard": ["Изменить"], "This json object describes the positioning of the widgets in the dashboard. It is dynamically generated when adjusting the widgets size and positions by using drag & drop in the dashboard view": ["Этот объект JSON описывает позиционирование виджетов на дашборде. Он генерируется динамически при изменении и перемещении графиков в дашборде"], "The CSS for individual dashboards can be altered here, or in the dashboard view where changes are immediately visible": ["CSS для отдельных панелей можно изменять здесь или в представлении панели, где изменения видны сразу"], "To get a readable URL for your dashboard": ["Для получения читаемого URL-адреса дашборда"], "This JSON object is generated dynamically when clicking the save or overwrite button in the dashboard view. It is exposed here for reference and for power users who may want to alter specific parameters.": ["Этот JSON-объект генерируется автоматически при сохранении или перезаписи дашборда. Он размещён здесь справочно и для опытных пользователей."], "Owners is a list of users who can alter the dashboard.": ["Владельцы – это список пользователей, которые могут изменять дашборд."], "Roles is a list which defines access to the dashboard. Granting a role access to a dashboard will bypass dataset level checks.If no roles are defined, regular access permissions apply.": ["Роли - это список, определяющий доступ к дашборду. Предоставление роли доступа к дашборду позволяет обойти проверки на уровне датасета. Если роли не определены, применяются обычные права доступа."], "Determines whether or not this dashboard is visible in the list of all dashboards": ["Определяет, виден ли этот дашборд в списке всех дашбордов"], "Dashboard": ["Дашборд"], "Title": ["Название"], "Roles": ["Роли"], "Published": ["Опубликовано"], "Position JSON": ["Позиция JSON"], "CSS": ["CSS"], "JSON Metadata": ["JSON Метаданные"], "Databases": ["Базы данных"], "Show Database": ["Показать базу данных"], "Add Database": ["Добавить базу данных"], "Edit Database": ["Редактировать Базу Данных"], "Expose this DB in SQL Lab": ["Предоставить доступ к базе в Лаборатории SQL"], "Operate the database in asynchronous mode, meaning that the queries are executed on remote workers as opposed to on the web server itself. This assumes that you have a Celery worker setup as well as a results backend. Refer to the installation docs for more information.": ["Работа с базой данных в асинхронном режиме означает, что запросы исполняются на удалённых воркерах, а не на веб-сервере Superset. Это подразумевает, что у вас есть установка с воркерами Celery. Обратитесь к документации по настройке за дополнительной информацией."], "Allow CREATE TABLE AS option in SQL Lab": ["Разрешить CREATE TABLE AS в Лаборатории SQL"], "Allow CREATE VIEW AS option in SQL Lab": ["Разрешить CREATE VIEW AS в Лаборатории SQL"], "Allow users to run non-SELECT statements (UPDATE, DELETE, CREATE, ...) in SQL Lab": ["Разрешить управление базой данных, используя запросы UPDATE, DELETE, CREATE и т.п. в Лаборатории SQL"], "When allowing CREATE TABLE AS option in SQL Lab, this option forces the table to be created in this schema": ["При включении опции CREATE TABLE AS в Лаборатории SQL, новые таблицы будут добавлены в эту схему"], "If Presto, all the queries in SQL Lab are going to be executed as the currently logged on user who must have permission to run them.<br/>If Hive and hive.server2.enable.doAs is enabled, will run the queries as service account, but impersonate the currently logged on user via hive.server2.proxy.user property.": ["Если Presto, все запросы в SQL Lab будут выполнены в качестве в настоящее время зарегистрированного пользователя, который должен иметь разрешение на их запуск.Если вы используете Presto, все запросы в Лаборатории SQL будут выполняться от авторизованного пользователя, который должен иметь разрешение на их выполнение. <br/> Если включены Hive и hive.server2.enable.doAs, то запросы будут выполняться через техническую учетную запись, но имперсонировать зарегистрированного пользователя можно через свойство hive.server2.proxy.user."], "Duration (in seconds) of the caching timeout for charts of this database. A timeout of 0 indicates that the cache never expires. Note this defaults to the global timeout if undefined.": ["Продолжительность (в секундах) таймаута кэширования для графиков этой базы данных.Таймаут 0 означает, что кэш никогда не очистится. Обратите внимание, что если значение не задано, применяется значение по умолчанию из основной конфигурации."], "If selected, please set the schemas allowed for csv upload in Extra.": ["Если выбрано, пожалуйста, установите схемы, разрешенные для загрузки CSV в дополнительной.Если установлено, выберите схемы, в которые разрешена загрузка CSV на вкладке \"Дополнительно\"."], "Expose in SQL Lab": ["Доступен в SQL редакторе"], "Allow CREATE TABLE AS": ["Разрешить CREATE TABLE AS"], "Allow CREATE VIEW AS": ["Разрешить CREATE VIEW AS"], "Allow DDL/DML": ["Разрешить DDL/DML"], "CTAS Schema": ["Схема CTAS"], "SQLAlchemy URI": ["SQLAlchemy URI"], "Chart Cache Timeout": ["Время жизни кэша графика"], "Secure Extra": ["Доп. безопасность"], "Root certificate": ["Корневой сертификат"], "Async Execution": ["Асинхронное выполнение"], "Impersonate the logged on user": ["Имперсонировать пользователя"], "Allow Csv Upload": ["Разрешить загрузку CSV"], "Backend": ["Др<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Extra field cannot be decoded by JSON. %(msg)s": ["Дополнительное поле не может быть декодировано с помощью JSON. %(msg)s"], "Invalid connection string, a valid string usually follows:'DRIVER://USER:PASSWORD@DB-HOST/DATABASE-NAME'<p>Example:'****************************************************'</p>": ["Недопустимая строка подключения, правильная строка обычно следует: 'Driver: // пользователь: пароль@db-host/database-name' <p> Пример: 'postgresql: // user: password@your-postgres-db/database' </p>Недопустимая строка для подключения, валидная строка соответствует шаблону:'ДРАЙВЕР://ИМЯ-ПОЛЬЗОВАТЕЛЯ:ПАРОЛЬ@ХОСТ/ИМЯ-БАЗЫ-ДАННЫХ'<p>Пример:'***********************************************'</p>"], "Request missing data field.": ["В запросе отсутствует поле с данными."], "Duplicate column name(s): %(columns)s": ["Повторяющееся имя столбца(ов): %(columns)s"], "Logs": ["Записи"], "Show Log": ["Показать запись"], "Add Log": ["Добавить запись"], "Edit Log": ["Редактировать запись"], "User": ["Пользователь"], "Action": ["Действие"], "dttm": ["Дата/время"], "JSON": ["JSON"], "Statements": ["Заявления"], "Show Statement": ["Показать заявление"], "Add Statement": ["Добавить заявку"], "Edit Statement": ["Редактировать заявление"], "is_external": ["is_external"], "is_new_team": ["is_new_team"], "Team slug": ["Читаемый URL команды"], "Finished": ["закончил"], "Request roles": ["Запрашиваемые роли"], "Created datetime": ["Создано DateTime"], "Last changed datetime": ["Последнее изменение DateTime"], "Show Team": ["Показать команду"], "Add Team": ["Добавить команду"], "Edit Team": ["Редактировать команду"], "Id": ["Id"], "Participants": ["участники"], "Time Range": ["Временной интервал"], "Time Column": ["Столбец даты/времени"], "Time Grain": ["Единица времени"], "Time Granularity": ["Гранулярность времени"], "Time": ["Время"], "A reference to the [Time] configuration, taking granularity into account": ["Ссылка на конфигурацию [Time], учитывая гранулярность"], "Aggregate": ["Агрегация"], "Raw records": ["Сырые записи"], "Category name": ["Название категории"], "Total value": ["Итоговое значение"], "Minimum value": ["Минимальное значение"], "Maximum value": ["Максимальное значение"], "Average value": ["Среднее значение"], "Count": ["Количество"], "Count Unique Values": ["Количество уникальных значений"], "List Unique Values": ["Список уникальных значений"], "Sum": ["Сумма"], "Average": ["Среднее"], "Median": ["Медиана"], "Sample Variance": ["Выборочная дисперсия"], "Sample Standard Deviation": ["Стандартное отклонение"], "Minimum": ["Мини<PERSON>ум"], "Maximum": ["Максимум"], "First": ["Первый"], "Last": ["Последний"], "Sum as Fraction of Total": ["Сумма как доля целого"], "Sum as Fraction of Rows": ["Сумма как доля строк"], "Sum as Fraction of Columns": ["Сумма как доля столбцов"], "Count as Fraction of Total": ["Количество, как доля от целого"], "Count as Fraction of Rows": ["Количество, как доля от строк"], "Count as Fraction of Columns": ["Количество, как доля от столбцов"], "Certified by %s": ["Утверждено: %s"], "description": ["описание"], "bolt": ["болт"], "Changing this control takes effect instantly": ["Изменение этого элемента применяется сразу"], "Show info tooltip": ["Показать информационную подсказку"], "SQL expression": ["Выражение SQL"], "Column type": ["Тип столбца"], "Column name": ["Имя столбца"], "Label": ["Метка"], "Metric name": ["Имя меры"], "unknown type icon": ["значок неизвестного типа"], "function type icon": ["значок типа функции"], "string type icon": ["значок типа строки"], "numeric type icon": ["значок числового типа"], "boolean type icon": ["значок логического типа"], "temporal type icon": ["значок временного типа"], "Advanced analytics": ["Расширенная аналитика"], "This section contains options that allow for advanced analytical post processing of query results": ["Этот раздел содержит варианты, которые позволяют проводить расширенную аналитическую пост -обработку результатов запросаВ этом разделе содержатся параметры, которые позволяют производить аналитическую постобработку результатов запроса"], "Rolling window": ["Скользящее окно"], "Rolling function": ["Скользящая средняя"], "None": ["Пусто"], "Defines a rolling window function to apply, works along with the [Periods] text box": ["Определяет функцию прокатного окна для применения, работает вместе с текстовым поле [периоды]Определяет функцию скользящего окна для применения, работает вместе с текстовым полем [Периоды]"], "Periods": ["Периоды"], "Defines the size of the rolling window function, relative to the time granularity selected": ["Определяет размер функции прокатного окна по сравнению с выбранной гранулярностью времениОпределяет размер функции скользящего окна относительно выбранной детализации по времени"], "Min periods": ["Минимальный период"], "The minimum number of rolling periods required to show a value. For instance if you do a cumulative sum on 7 days you may want your \"Min Period\" to be 7, so that all data points shown are the total of 7 periods. This will hide the \"ramp up\" taking place over the first 7 periods": ["Минимальное количество периодов прокатки, необходимых для показа значения. Минимальное количество скользящих периодов, необходимое для отображения значения. Например, если вы делаете накопительную сумму за 7 дней, вы можете указать, чтобы \"Минимальный период\" был равен 7, так что все показанные точки данных представляют собой общее количество 7 периодов"], "Time comparison": ["Столбец с датой"], "Time shift": ["Временной сдвиг"], "1 day ago": ["1 день назад"], "1 week ago": ["1 неделя назад"], "28 days ago": ["28 дней назад"], "30 days ago": ["30 дней назад"], "52 weeks ago": ["52 недели назад"], "1 year ago": ["1 год назад"], "104 weeks ago": ["104 недели назад"], "2 years ago": ["2 года назад"], "156 weeks ago": ["156 недель назад"], "3 years ago": ["3 года назад"], "Overlay one or more timeseries from a relative time period. Expects relative time deltas in natural language (example:  24 hours, 7 days, 52 weeks, 365 days). Free text is supported.": ["Наложение одной или нескольких временных рядов из относительного периода времени."], "Calculation type": ["Тип расчёта"], "Actual values": ["Фактические значения"], "Difference": ["Разница"], "Percentage change": ["Процентное изменение"], "Ratio": ["Отношение"], "How to display time shifts: as individual lines; as the difference between the main time series and each time shift; as the percentage change; or as the ratio between series and time shifts.": ["Как отображать смены времени: как отдельные строки; Как отображать смещения во времени: как отдельные линии; как абсолютную разницу между основным временным рядом и каждым смещением; как процентное изменение; или как соотношение между рядами и смещениями."], "Resample": ["Ресемплирование (изменение частоты данных)"], "Rule": ["Правило"], "1 minutely frequency": ["Минутная частота"], "1 hourly frequency": ["Часовая частота"], "1 calendar day frequency": ["Дневная частота"], "7 calendar day frequency": ["Недельная частота"], "1 month start frequency": ["Месячная частота (начало месяца)"], "1 month end frequency": ["Месячная частота (конец месяца)"], "1 year start frequency": ["Годовая частота (начало года)"], "1 year end frequency": ["Годовая частота (конец года)"], "Pandas resample rule": ["Правило ресемплирования данных библиотек<PERSON> Pandas"], "Fill method": ["Метод заполнения пропусков"], "Null imputation": ["Пустые значения"], "Zero imputation": ["Нулевые значения"], "Linear interpolation": ["Линейная интерполяция"], "Forward values": ["Будущие значения"], "Backward values": ["Предыдущие значения"], "Median values": ["Медианные значения"], "Mean values": ["Средние значения"], "Sum values": ["Суммарные значения"], "Pandas resample method": ["Метод ресемплирования данных библиотек<PERSON> Pandas"], "Annotations and Layers": ["Аннотации и слои"], "Annotation Layers": ["Слои аннотаций"], "Left": ["Слева"], "Top": ["Сверху"], "Chart Title": ["Название графика"], "X Axis": ["Ось X"], "X Axis Title": ["Название оси X"], "X AXIS TITLE BOTTOM MARGIN": ["Отступ снизу названия оси X"], "Y Axis": ["Ось Y"], "Y Axis Title": ["Название оси Y"], "Y Axis Title Margin": ["Отступ от заголовка оси Y"], "Y Axis Title Position": ["Заглавная позиция оси Y"], "Query": ["Запрос"], "Predictive Analytics": ["Предиктивная аналитика"], "Enable forecast": ["Включить прогноз в график"], "Enable forecasting": ["Включить прогнозирование данных"], "Forecast periods": ["Кол-во прогнозных периодов"], "How many periods into the future do we want to predict": ["На сколько периодов в будущем предсказывать"], "Confidence interval": ["Доверительный интервал"], "Width of the confidence interval. Should be between 0 and 1": ["Ширина доверительного интервала. Должна быть между 0 и 1"], "Yearly seasonality": ["Годовая сезонность"], "default": ["по умолчанию"], "Yes": ["Да"], "No": ["Нет"], "Should yearly seasonality be applied. An integer value will specify Fourier order of seasonality.": ["Должен быть применен ежегодный сезонность. Применяется годовая сезонность. Целочисленное значение будет указывать порядок сезонности Фурье."], "Weekly seasonality": ["Недельная сезонность"], "Should weekly seasonality be applied. An integer value will specify Fourier order of seasonality.": ["Должен быть применен еженедельный сезонность. Применяется недельная сезонность. Целочисленное значение будет указывать порядок сезонности Фурье."], "Daily seasonality": ["Дневная сезонность"], "Should daily seasonality be applied. An integer value will specify Fourier order of seasonality.": ["Должен применяться ежедневная сезонность. Применяется дневная сезонность. Целочисленное значение будет указывать порядок сезонности Фурье."], "Time related form attributes": ["Параметры, связанные со временем"], "Datasource & Chart Type": ["Источник данных и Тип графика"], "Chart ID": ["ID графика"], "The id of the active chart": ["Идентифика<PERSON>ор активного графика"], "Cache Timeout (seconds)": ["Время жизни кэша (секунды)"], "The number of seconds before expiring the cache": ["Количество секунд до истечения срока действия кэша"], "URL Parameters": ["Параметры URL"], "Extra url parameters for use in Jinja templated queries": ["Дополнительные url параметры для запросов, использующих шаблонизацию Jinja"], "Extra Parameters": ["Доп. параметры"], "Extra parameters that any plugins can choose to set for use in Jinja templated queries": ["Дополнительные параметры, которые любые плагины могут выбрать для использования в шаблонных запросах джинджиДополнительные параметры для шаблонизации Jinja, которые могут быть использованы в графиках"], "Color Scheme": ["Цветовая схема"], "1 month ago": ["1 месяц назад"], "Custom date": ["Пользовательская дата"], "Inherit range from time filter": ["Наследуйте диапазон от фильтра времени"], "Time Comparison": ["Сравнение по времени"], "Compare results with other time periods.": ["Сравните результаты с другими временными периодами."], "Select or type a custom value...": ["Выберите или введите пользовательское значение..."], "Overlay results from a relative time period. Expects relative time deltas in natural language (example:  24 hours, 7 days, 52 weeks, 365 days). Free text is supported. Use \"Inherit range from time filters\" to shift the comparison time range by the same length as your time range and use \"Custom\" to set a custom comparison range.": ["результаты наложения относительного периода времени. Наложение результатов относительного периода времени. Ожидает относительных временных дельт на естественном языке (пример: 24 часа, 7 дней, 52 недели, 365 дней). Поддерживается свободный текст. Используйте \"Inherit range from time filters\", чтобы сместить временной диапазон сравнения на ту же длину, что и ваш временной диапазон, и используйте \"Custom\", чтобы задать пользовательский диапазон сравнения."], "shift start date": ["дата начала смены"], "Contribution Mode": ["Режим относительных значений"], "Row": ["Строка"], "Series": ["<PERSON>яд"], "Calculate contribution per series or row": ["Рассчитайте взнос на серию или строкуВычислить вклад в общую сумму (долю) по категории или строке. Установливает формат показателя в проценты"], "Y-Axis Sort By": ["оси y сортируют"], "X-Axis Sort By": ["Сортировка оси X"], "Decides which column to sort the base axis by.": ["Решает, какой столбец сортирует базовую ось."], "Y-Axis Sort Ascending": ["Ось оси сортировки"], "X-Axis Sort Ascending": ["Сортировать по возрастанию оси X"], "Whether to sort ascending or descending on the base Axis.": ["Сортировать ли поднимание или спуск по базовой оси."], "Force categorical": ["Категориальная сила"], "Treat values as categorical.": ["Рассматривать значения как категориальные."], "Decides which measure to sort the base axis by.": ["Решает, какая измерение для сортировки базовой оси по."], "Dimensions": ["Измерения"], "Dimensions contain qualitative values such as names, dates, or geographical data. Use dimensions to categorize, segment, and reveal the details in your data. Dimensions affect the level of detail in the view.": ["Размеры содержат качественные значения, такие как имена, даты или географические данные."], "Add dataset columns here to group the pivot table columns.": ["Добавьте столбцы набора данных здесь, чтобы сгруппировать столбцы таблицы Pivot."], "Dimension": ["Измерение"], "Defines the grouping of entities. Each series is represented by a specific color in the chart.": ["определяет группировку сущностей."], "Entity": ["Элемент"], "This defines the element to be plotted on the chart": ["Элемент, который будет отражен на графике"], "Filters": ["Фильтры"], "Select one or many metrics to display. You can use an aggregation function on a column or write custom SQL to create a metric.": ["Выберите один или много метрик для отображения. Выберите одну или несколько метрик для отображения. Вы можете использовать функцию агрегирования на столбце или написать собственный SQL для создания метрики."], "Select a metric to display. You can use an aggregation function on a column or write custom SQL to create a metric.": ["Выберите метрику для отображения. Для создания метрики можно использовать агрегатную функцию для столбца или написать собственный SQL."], "Right Axis Metric": ["Мера для правой оси"], "Select a metric to display on the right axis": ["Выберите метрику для отображения на правой оси"], "Sort by": ["Сортировка"], "This metric is used to define row selection criteria (how the rows are sorted) if a series or row limit is present. If not defined, it reverts to the first metric (where appropriate).": ["Этот показатель используется для определения критериев выбора строк (как строки отсортируются), если присутствует предел ряда или строки. Эта метрика используется для определения критериев отбора строк (как сортируются строки),  если присутствует ограничение на набор или строку. Если она не определена, она возвращается к первой метрике (где это уместно)."], "Bubble Size": ["Размер пузыря"], "Metric used to calculate bubble size": ["Мера, используемая для расчета размера пузыря"], "The dataset column/metric that returns the values on your chart's x-axis.": ["Столбец/метрика датасета, который возвращает значения по оси X в графике."], "The dataset column/metric that returns the values on your chart's y-axis.": ["Столбец/метрика датасета, который возвращает значения по оси Y графика."], "Color Metric": ["Цвет меры"], "A metric to use for color": ["Показатель, используемый для расчета цвета"], "The time column for the visualization. Note that you can define arbitrary expression that return a DATETIME column in the table. Also note that the filter below is applied against this column or expression": ["Столбец времени для визуализации. Столбец данных формата дата/время. Вы можете определить произвольное выражение, которое будет возвращать столбец даты/времени в таблице. Фильтр ниже будет применён к этому столбцу или выражению"], "Drop a temporal column here or click": ["Перетащите столбец формата дата/время сюда"], "Y-axis": ["Ось Y"], "Dimension to use on y-axis.": ["Измерение для использования на оси Y."], "X-axis": ["Ось X"], "Dimension to use on x-axis.": ["Измерение для использования на оси X."], "The type of visualization to display": ["Выберите необходимый тип визуализации"], "Fixed Color": ["Фиксированный цвет"], "Use this to define a static color for all circles": ["Этот цвет используется для заливки"], "Linear Color Scheme": ["Линейная цветовая схема"], "all": ["Все"], "5 seconds": ["5 секунд"], "30 seconds": ["30 секунд"], "1 minute": ["1 минута"], "5 minutes": ["5 минут"], "30 minutes": ["30 минут"], "1 hour": ["1 час"], "6 hour": ["6 часов"], "1 day": ["1 день"], "7 days": ["7 дней"], "week": ["неделя"], "week starting Sunday": ["неделя, начинающаяся в воскресенье"], "week ending Saturday": ["неделя, заканчивающаяся в субботу"], "month": ["мес<PERSON><PERSON>"], "quarter": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "year": ["год"], "The time granularity for the visualization. Note that you can type and use simple natural language as in `10 seconds`, `1 day` or `56 weeks`": ["Время гранулярность для визуализации. Интервал времени, в границах которого строится график. Обратите внимание, что для определения диапазона времени, вы можете использовать естественный язык. Например, можно указать словосочетания - «10 seconds», «1 day» или «56 weeks»"], "Select a time grain for the visualization. The grain is the time interval represented by a single point on the chart.": ["Выберите зерно времени для визуализации."], "This control filters the whole chart based on the selected time range. All relative times, e.g. \"Last month\", \"Last 7 days\", \"now\", etc. are evaluated on the server using the server's local time (sans timezone). All tooltips and placeholder times are expressed in UTC (sans timezone). The timestamps are then evaluated by the database using the engine's local timezone. Note one can explicitly set the timezone per the ISO 8601 format if specifying either the start and/or end time.": ["Этот элемент управления фильтрует весь график на основе выбранного диапазона времени. Все относительное время, например, \"Последний месяц\", \"Последние 7 дней\", \"Сейчас\" и т.д. оцениваются на сервере, используя местное время сервера (без учета часового пояса). Все всплывающие подсказки и время, указанное в плагине, выражается в UTC (без учета часового пояса). Временные метки затем оцениваются базой данных с использованием локального часового пояса движка. Обратите внимание, что можно явно задать часовой пояс в соответствии с форматом ISO 8601, если указать начальное и/или конечное время"], "Row limit": ["Лимит строк"], "Limits the number of the rows that are computed in the query that is the source of the data used for this chart.": ["Ограничивает количество строк, которые вычисляются в запросе, который является источником данных, используемым для этого графика."], "Sort Descending": ["Сортировать по убыванию"], "If enabled, this control sorts the results/values descending, otherwise it sorts the results ascending.": ["Если включено, этот элемент управления сортирует результаты/значения, спускающиеся, в противном случае он сортирует результаты восходящими."], "Series limit": ["Лимит кол-ва категорий"], "Limits the number of series that get displayed. A joined subquery (or an extra phase where subqueries are not supported) is applied to limit the number of series that get fetched and rendered. This feature is useful when grouping by high cardinality column(s) though does increase the query complexity and cost.": ["Ограничивает количество отображаемых серий. Объединенный подзапрос (или дополнительная фаза, если подзапросы не поддерживаются) применяется для ограничения количества серий, которые будут получены и отображены. Эта функция полезна при группировке по столбцам с высокой кардинальностью, но увеличивает сложность и стоимость запроса."], "Y Axis Format": ["Формат Оси Y"], "Currency format": ["Формат валюты"], "Time format": ["Формат даты/времени"], "The color scheme for rendering chart": ["Цветовая схема, применяемая для раскрашивания графика"], "Truncate Metric": ["Убрать имя меры"], "Whether to truncate metrics": ["Нужно ли усекать метрики"], "Show empty columns": ["Показывать пустые столбцы"], "Sort by metric": ["Сортировка по мере"], "Whether to sort results by the selected metric in descending order.": ["Сортировка результатов по выбранной мере в порядке убывания."], "Size format": ["Формат размера"], "D3 format syntax: https://github.com/d3/d3-format": ["Формат D3: https://github.com/d3/d3-format."], "Only applies when \"Label Type\" is set to show values.": ["Применяется только тогда, когда \\ \"метка типа \" установлен для отображения значений."], "Only applies when \"Label Type\" is not set to a percentage.": ["Применяется только тогда, когда \\ \"метка типа \" не установлен на процент."], "With space": ["С отступом"], "With space rounded": ["С пробелом округл."], "Rounded": ["Округленный"], "With space rounded 1": ["С пробелом округл. 1"], "With space rounded 2": ["С пробелом округл. 2"], "With space rounded 3": ["С пробелом округл. 3"], "Adaptive formatting": ["Адаптивное форматирование"], "Original value": ["Оригинальное значение"], "Duration in ms (66000 => 1m 6s)": ["Продолжительность в мс (66000 => 1m 6s)"], "Duration in ms (1.40008 => 1ms 400µs 80ns)": ["Продолжительность в мс (1.40008 => 1ms 400µs 80ns)"], "Duration in ms (66000 => 0:01:06)": ["Продолжительность в мс (66000 => 0:01:06)"], "D3 time format syntax: https://github.com/d3/d3-time-format": ["Формат времени D3: https://github.com/d3/d3-time-format"], "Oops! An error occurred!": ["Произошла ошибка!"], "Stack Trace:": ["Stack Trace:"], "No results were returned for this query. If you expected results to be returned, ensure any filters are configured properly and the datasource contains data for the selected time range.": ["Результаты не были возвращены для этого запроса. По этому запросу не было возвращено данных. Если вы ожидали увидеть результаты, убедитесь, что все фильтры настроены правильно и источник данных содержит записи для заданного временного интервала."], "No Results": ["Нет результатов"], "ERROR": ["ОШИБКА"], "This chart uses features or modules which are no longer actively maintained. It will eventually be replaced or removed.": ["В этой таблице используются функции или модули, которые больше не поддерживаются. Со временем они будут заменены или удалены."], "This chart was tested and verified, so the overall experience should be stable.": ["Эта таблица была протестирована и проверена, поэтому общее впечатление должно быть стабильным."], "Found invalid orderby options": ["Найдено неверные варианты заказа"], "Unknown error": ["Неизвестная ошибка"], "Invalid input": ["Недопустимые входные данные"], "Unexpected error: ": ["Неожиданная ошибка: "], "(no description, click to see stack trace)": ["(нет описания, нажмите для просмотра трассировки стека)"], "Network error": ["Ошибка сети"], "Request timed out": ["Вышло время запроса"], "Issue 1000 - The dataset is too large to query.": ["Ошибка 1000 - Источник данных слишком велик для запроса."], "Issue 1001 - The database is under an unusual load.": ["Ошибка 1001 - Нетипичная загрузка базы данных."], "An error occurred": ["Произошла ошибка"], "Sorry, an unknown error occurred.": ["Извините, произошла неизвестная ошибка."], "Sorry, there was an error saving this %s: %s": ["Извините, была ошибка, которая спасала этот %s: %s"], "You do not have permission to edit this %s": ["У вас нет разрешения на редактирование этого %s"], "No data": ["Нет данных"], "is expected to be an integer": ["Ожидается целое число"], "is expected to be a number": ["Ожидается число"], "is expected to be a Mapbox URL": ["ожидает<PERSON>я, что будет Mapbox URL"], "Value cannot exceed %s": ["Значение не может превышать %s"], "cannot be empty": ["Необходимо заполнить"], "Filters for comparison must have a value": ["фильтры для сравнения должны иметь значение"], "Domain": ["До<PERSON><PERSON>н"], "hour": ["час"], "day": ["день"], "The time unit used for the grouping of blocks": ["Единица времени для группировки блоков"], "Subdomain": ["Подблок"], "min": ["Мини<PERSON>ум"], "The time unit for each block. Should be a smaller unit than domain_granularity. Should be larger or equal to Time Grain": ["время времени для каждого блока. Единица времени для каждого подблока. Должна быть меньшей единицей, чем единица времени блока. Должно быть больше или равно единице времени"], "Chart Options": ["Свойства графика"], "Cell Size": ["Размер ячейки"], "The size of the square cell, in pixels": ["Размер квадратной ячейки (в пикселях)"], "Cell Padding": ["Расстояние между ячейками"], "The distance between cells, in pixels": ["Расстояние между ячейками (в пикселях)"], "Cell Radius": ["Радиус ячейки"], "The pixel radius": ["Радиус ячейки (в пикселях)"], "Color Steps": ["Количество цветов"], "The number color \"steps\"": ["Количество цветов в цветовой схеме"], "Time Format": ["Формат даты/времени"], "Legend": ["Легенда"], "Whether to display the legend (toggles)": ["Отображать легенду (переключатель)"], "Show Values": ["Показать значения"], "Whether to display the numerical values within the cells": ["Отображение числовых значений в ячейках"], "Show Metric Names": ["Показать имена мер"], "Whether to display the metric name as a title": ["Отображать имя меры как названия"], "Number Format": ["Числовой формат"], "Correlation": ["Корреляция"], "Visualizes how a metric has changed over a time using a color scale and a calendar view. Gray values are used to indicate missing values and the linear color scheme is used to encode the magnitude of each day's value.": ["Визуализ<PERSON><PERSON><PERSON><PERSON><PERSON>, как метрика изменилась за время, используя цветовой шкалу и календарный вид. Визуализирует, как показатель изменился с течением времени, используя цветовую шкалу и календарь. Значения серого цвета используются для обозначения отсутствующих значений, а линейная цветовая схема используется для отображения величины значения каждого дня."], "Business": ["Бизнес"], "Comparison": ["Сравнение"], "Intensity": ["Насыщенность"], "Pattern": ["Пат<PERSON><PERSON><PERSON><PERSON>"], "Report": ["Отчет"], "Trend": ["Тенденция"], "less than {min} {name}": ["меньше, чем {min} {name}"], "between {down} and {up} {name}": ["между {down} и {up} {name}"], "more than {max} {name}": ["больше, чем {max} {name}"], "Number format": ["Числовой формат"], "Choose a number format": ["Выберите числовой формат"], "Source": ["Источник"], "Choose a source": ["Выберите источник"], "Target": ["Цель"], "Choose a target": ["Выберите цель"], "Flow": ["Поток"], "Showcases the flow or link between categories using thickness of chords. The value and corresponding thickness can be different for each side.": ["демонстрирует поток или связь между категориями, используя толщину аккордов."], "Relationships between community channels": ["отношения между каналами сообщества"], "Chord Diagram": ["Хордовая диаграмма"], "Circular": ["Круглая форма"], "Legacy": ["Устарел"], "Proportional": ["Пропорция"], "Relational": ["Относительный"], "Which country to plot the map for?": ["Выбор страны для графика"], "ISO 3166-2 Codes": ["Коды ISO 3166-2"], "Column containing ISO 3166-2 codes of region/province/department in your table.": ["Столбец, содержащий коды ISO 3166-2 региона/республики/области в вашей таблице."], "Metric to display bottom title": ["Мера для отображения нижнего заголовка"], "Map": ["Карта"], "Visualizes how a single metric varies across a country's principal subdivisions (states, provinces, etc) on a choropleth map. Each subdivision's value is elevated when you hover over the corresponding geographic boundary.": ["Визуализи<PERSON><PERSON><PERSON><PERSON>, как единственная метрика варьируется в основных подразделениях страны (штаты, провинции и т. Д.) На карте чороплат."], "2D": ["2D карты"], "Geo": ["Карта"], "Range": ["Интервал"], "Stacked": ["С наполнением"], "Sorry, there appears to be no data": ["Извините, похоже, что данные отсутствуют"], "Event definition": ["Определение события"], "Event Names": ["Имена событий"], "Columns to display": ["Столбцы для отображения"], "Order by entity id": ["Сортировку по идентификатору объекта"], "Important! Select this if the table is not already sorted by entity id, else there is no guarantee that all events for each entity are returned.": ["Важно! Выберите этот вариант, если таблица еще не отсортирована по идентификатору сущности, иначе нет гарантии, что будут возвращены все события для каждой сущности."], "Minimum leaf node event count": ["Минимальное количество событий листового узла"], "Leaf nodes that represent fewer than this number of events will be initially hidden in the visualization": ["Листовые узлы, которые представляют меньше этого количества событий, будут изначально скрыты в визуализации"], "Additional metadata": ["Дополнительные метаданные"], "Metadata": ["Метаданные"], "Select any columns for metadata inspection": ["Выберите любые столбцы для проверки метаданных"], "Entity ID": ["ID элемента"], "e.g., a \"user id\" column": ["например, столбец \"идентификатор пользователя\""], "Max Events": ["Лимит событий"], "The maximum number of events to return, equivalent to the number of rows": ["Максимальное количество возвращаемых событий, эквивалентно количеству строк"], "Compares the lengths of time different activities take in a shared timeline view.": ["Сравнивает продолжительность времени, которые различные действия принимают в общий вид сроки."], "Event Flow": ["Поток событий"], "Progressive": ["Постепенный"], "Axis ascending": ["Ось по возрастанию"], "Axis descending": ["Ось по убыванию"], "Metric ascending": ["Мера по возрастанию"], "Metric descending": ["Мера по убыванию"], "Heatmap Options": ["Настройки тепловой карты"], "XScale Interval": ["XScale интервал"], "Number of steps to take between ticks when displaying the X scale": ["Количество шагов, которые необходимо предпринять между клещами при отображении X Scale"], "YScale Interval": ["интервал YSCALE"], "Number of steps to take between ticks when displaying the Y scale": ["Количество шагов, которые необходимо предпринять между клещами при отображении шкалы Y"], "Rendering": ["Отрисовка"], "pixelated (Sharp)": ["пикселированный (Sharp)"], "auto (Smooth)": ["Автоматически (плавно)"], "image-rendering CSS attribute of the canvas object that defines how the browser scales up the image": ["Атрибут CSS-Rendering-Rendering изображения объекта Canvas, который определяет, как браузер масштабирует изображение"], "Normalize Across": ["Нормализовать по всему"], "heatmap": ["тепловая карта"], "x": ["x"], "y": ["y"], "Color will be shaded based the normalized (0% to 100%) value of a given cell against the other cells in the selected range: ": ["Цвет будет затенен на основе нормализованного (от 0% до 100%) значения данной ячейки против других ячеек в выбранном диапазоне: "], "x: values are normalized within each column": ["x: значения нормализованы внутри каждого столбца"], "y: values are normalized within each row": ["y: значения нормализованы внутри каждой строки"], "heatmap: values are normalized across the entire heatmap": ["тепловая карта: значения нормализованы внутри всей карты"], "Left Margin": ["Левый отступ"], "auto": ["Автоматически"], "Left margin, in pixels, allowing for more room for axis labels": ["Левый отступ (в пикселях), дает больше пространства меткам оси"], "Bottom Margin": ["Нижний отступ"], "Bottom margin, in pixels, allowing for more room for axis labels": ["Нижний отступ (в пикселях), да<PERSON>т больше пространства меткам оси"], "Value bounds": ["Ограничения для значения"], "Hard value bounds applied for color coding. Is only relevant and applied when the normalization is applied against the whole heatmap.": ["Жесткие границы значений, применяемые для цветового кодирования. Применяется только в том случае, если нормализация применяется ко всей тепловой карте."], "Sort X Axis": ["Сортировка оси X"], "Sort Y Axis": ["Сортировка оси Y"], "Show percentage": ["Показывать долю"], "Whether to include the percentage in the tooltip": ["Отображение процентной доли во всплывающей подсказке"], "Normalized": ["Нормализовать"], "Whether to apply a normal distribution based on rank on the color scale": ["Применять нормальное распределение на основе ранга в цветовой схеме"], "Value Format": ["Формат значения"], "Visualize a related metric across pairs of groups. Heatmaps excel at showcasing the correlation or strength between two groups. Color is used to emphasize the strength of the link between each pair of groups.": ["Визуализируйте связанную метрику между парами групп. Тепловые карты отлично демонстрируют корреляцию или силу связи между двумя группами. Цвет используется для того, чтобы подчеркнуть силу связи между каждой парой групп."], "Sizes of vehicles": ["Размеры транспортных средств"], "Employment and education": ["Трудоустройство и образование"], "Heatmap (legacy)": ["Heatmap (legacy)"], "Density": ["Концентрация"], "Predictive": ["Прогноз"], "Single Metric": ["Одна мера"], "Deprecated": ["Устарело"], "to": ["по"], "count": ["количество"], "cumulative": ["кумулятивно"], "percentile (exclusive)": ["Перцентиль (исключая)"], "Select the numeric columns to draw the histogram": ["Выберите числовые столбцы для отрисовки гистограммы"], "No of Bins": ["Количество столбцов"], "Select the number of bins for the histogram": ["Выберите количество столбцов для гистограммы"], "X Axis Label": ["Метка оси X"], "Y Axis Label": ["Метка оси Y"], "Whether to normalize the histogram": ["Нормализовать гистограмму"], "Cumulative": ["С накоплением"], "Whether to make the histogram cumulative": ["Сделать гистограмму нарастающей"], "Distribution": ["Распределение"], "Take your data points, and group them into \"bins\" to see where the densest areas of information lie": ["Возьмите свои точки данных и группируйте их в \\ \"Bins \", чтобы увидеть, где лежат самые плотные области информации"], "Population age data": ["Lанные по возрасту населения"], "Histogram (legacy)": ["Гистограмма (legacy)"], "Contribution": ["Режим относительных значений"], "Compute the contribution to the total": ["Вычислить вклад в общую сумму (долю)"], "Series Height": ["Высота рядов"], "Pixel height of each series": ["Высота каждого ряда (в пикселях)"], "Value Domain": ["Домен значения"], "series": ["категории"], "overall": ["в целом"], "change": ["изменение"], "series: Treat each series independently; overall: All series use the same scale; change: Show changes compared to the first data point in each series": ["серии: Относитесь к каждой серии независимо; общий: Во всех сериях используется одна и та же шкала; изменение: Показать изменения по сравнению с первой точкой данных в каждой серии."], "Compares how a metric changes over time between different groups. Each group is mapped to a row and change over time is visualized bar lengths and color.": ["Сравнивает, как изменяется метрика с течением времени между различными группами. Каждая группа отображается в строке, а изменения с течением времени визуализируются длиной и цветом столбцов."], "Horizon Chart": ["Horizon Chart"], "Dark Cyan": ["Темно-голубой"], "Purple": ["Фиолетовый"], "Gold": ["Золотой"], "Dim Gray": ["Тускло-серый"], "Crimson": ["Малиновый"], "Forest Green": ["Лесной зеленый"], "Longitude": ["Долгота"], "Column containing longitude data": ["Столбец, содержащий данные о долготе"], "Latitude": ["Шир<PERSON><PERSON>а"], "Column containing latitude data": ["Столбец, содержащий данные о широте"], "Clustering Radius": ["Радиус кластера"], "The radius (in pixels) the algorithm uses to define a cluster. Choose 0 to turn off clustering, but beware that a large number of points (>1000) will cause lag.": ["Радиус (в пикселях), который алгоритм использует для определения кластера."], "Points": ["Точки"], "Point Radius": ["Радиус маркера"], "The radius of individual points (ones that are not in a cluster). Either a numerical column or `Auto`, which scales the point based on the largest cluster": ["Радиус отдельных точек (те, которые не находятся в кластере). Радиус маркеров (не находящихся в кластере). Выберите числовой столбец или `Автоматически`, который отмасштабирует маркеры по наибольшему маркеру"], "Auto": ["Автоматически"], "Point Radius Unit": ["Единица измерения радиуса маркера"], "Pixels": ["Пиксели"], "Miles": ["<PERSON>или"], "Kilometers": ["Километры"], "The unit of measure for the specified point radius": ["Единица измерения для указанного радиуса маркера"], "Labelling": ["Маркировка"], "label": ["метка"], "`count` is COUNT(*) if a group by is used. Numerical columns will be aggregated with the aggregator. Non-numerical columns will be used to label points. Leave empty to get a count of points in each cluster.": ["`count` - это COUNT(*), если используется группировка. Числовые столбцы будут агрегированы с помощью агрегатора. Нечисловые столбцы будут использоваться для обозначения точек. Оставьте пустым, чтобы получить подсчет точек в каждом кластере."], "Cluster label aggregator": ["Агрегатор меток кластера"], "sum": ["Сумма"], "mean": ["Среднее"], "max": ["Максимум"], "std": ["Стандартное отклонение"], "var": ["Дисперсия"], "Aggregate function applied to the list of points in each cluster to produce the cluster label.": ["Агрегатная функция, применяемая к списку точек в каждом кластере для получения метки кластера.Агрегатная функция, применяемая для списка точек в каждом кластере для создания метки кластера."], "Visual Tweaks": ["Визуальные настройки"], "Live render": ["Мгновенная отрисовка"], "Points and clusters will update as the viewport is being changed": ["Точки и кластеры будут обновляться по мере изменения области просмотра"], "Map Style": ["Стиль карты"], "Streets": ["Схема"], "Dark": ["Темный"], "Light": ["Светлый"], "Satellite Streets": ["Гибридный режим"], "Satellite": ["Спутник"], "Outdoors": ["Туристический режим"], "Base layer map style. See Mapbox documentation: %s": ["Стиль карты базовых слоев. Смотреть документацию Mapbox: %s"], "Opacity": ["Прозрачность"], "Opacity of all clusters, points, and labels. Between 0 and 1.": ["Непрозрачность всех кластеров, точек и меток. Между 0 и 1."], "RGB Color": ["Цвет RGB"], "The color for points and clusters in RGB": ["Цвет для маркеров и кластеров в RGB"], "Viewport": ["Область просмотра"], "Default longitude": ["Долгота по умолчанию"], "Longitude of default viewport": ["Долгота для области просмотра"], "Default latitude": ["Широта по умолчанию"], "Latitude of default viewport": ["Широта для области просмотра"], "Zoom": ["Масштабирование"], "Zoom level of the map": ["Уровень масштабирования карты"], "One or many controls to group by. If grouping, latitude and longitude columns must be present.": ["один или много элементов управления для группы."], "Light mode": ["Светлый режим"], "Dark mode": ["Темная тема"], "MapBox": ["Mapbox"], "Scatter": ["Точечный"], "Transformable": ["Трансформируемый"], "Significance Level": ["Уровень значимости"], "Threshold alpha level for determining significance": ["Пороговый альфа-уровень для определения значимости"], "p-value precision": ["точность p-значения"], "Number of decimal places with which to display p-values": ["Количество десятичных значений для демонстрации p-значений"], "Lift percent precision": ["Процент подъема"], "Number of decimal places with which to display lift values": ["Количество десятичных знаков для демонстрации значений подъема"], "Table that visualizes paired t-tests, which are used to understand statistical differences between groups.": ["Таблица, которая визуализирует парные t-тесты, которые используются для понимания статистических различий между группами.Таблица, визуализирующая парные t-тесты, которые используются для нахождения статистических различий между группами."], "Paired t-test Table": ["Таблица парного t-теста"], "Statistical": ["Статистический учет"], "Tabular": ["Таблицы"], "Options": ["Опции"], "Data Table": ["Таблица"], "Whether to display the interactive data table": ["Отобража<PERSON>ь интерактивную таблицу с данными"], "Include Series": ["Включать серию"], "Include series name as an axis": ["Включить имена категорий в качестве оси"], "Ranking": ["Ранжирование"], "Plots the individual metrics for each row in the data vertically and links them together as a line. This chart is useful for comparing multiple metrics across all of the samples or rows in the data.": ["Заклинает отдельные метрики для каждой строки в вертикали данных и связывает их вместе как строка."], "Directional": ["Направленный"], "Time Series Options": ["Настройки временных рядов"], "Not Time Series": ["Не временные ряды"], "Ignore time": ["Игнорировать время"], "Time Series": ["Временной ряд"], "Standard time series": ["Стандартные временные ряды"], "Aggregate Mean": ["Агрегированное среднее"], "Mean of values over specified period": ["Среднее значений за указанный период"], "Aggregate Sum": ["Агрегированная сумма"], "Sum of values over specified period": ["Сумма значений за обозначенный период"], "Metric change in value from `since` to `until`": ["Изменение меры с `до` до `после`"], "Percent Change": ["Процентное изменение"], "Metric percent change in value from `since` to `until`": ["Процентное изменение меры с `до` до `после`"], "Factor": ["Фактор"], "Metric factor change from `since` to `until`": ["Изменение метрического фактора с `с` до `"], "Advanced Analytics": ["Расширенная аналитика"], "Use the Advanced Analytics options below": ["Используйте настройки Расширенной аналитики ниже"], "Settings for time series": ["Настройки временных рядов"], "Date Time Format": ["Формат даты и времени"], "Partition Limit": ["Количество разбиений"], "The maximum number of subdivisions of each group; lower values are pruned first": ["Максимальное количество подразделений каждой группы; "], "Partition Threshold": ["Порог раздела"], "Partitions whose height to parent height proportions are below this value are pruned": ["Разделы, пропорции высоты от высоты от родителей ниже этого значения обрезаны"], "Log Scale": ["Логарифмическая шкала"], "Use a log scale": ["Использовать логарифмическую шкалу"], "Equal Date Sizes": ["Одинаковые размеры дат"], "Check to force date partitions to have the same height": ["Проверьте, чтобы принудительно дата разбил, чтобы иметь одинаковую высоту"], "Rich Tooltip": ["Расширенная всплывающая подсказка"], "The rich tooltip shows a list of all series for that point in time": ["Расширенная всплывающая подсказка показывает список всех категорий для этой точки."], "Rolling Window": ["Скользящее окно"], "Rolling Function": ["Скользящая средняя"], "cumsum": ["Кумулятивная сумма"], "Min Periods": ["Минимальный период"], "Time Shift": ["Временной сдвиг"], "1 week": ["1 неделя"], "28 days": ["28 д<PERSON>ей"], "30 days": ["30 дней"], "52 weeks": ["52 недели"], "1 year": ["1 год"], "104 weeks": ["104 недели"], "2 years": ["2 года"], "156 weeks": ["156 недель"], "3 years": ["3 года"], "Overlay one or more timeseries from a relative time period. Expects relative time deltas in natural language (example: 24 hours, 7 days, 52 weeks, 365 days). Free text is supported.": ["наложенный один или несколько раз из относительного периода времени."], "Actual Values": ["Фактические значения"], "1T": ["1МИН"], "1H": ["1Ч"], "1D": ["1Д"], "7D": ["7Д"], "1M": ["1М"], "1AS": ["1С"], "Method": ["Метод"], "asfreq": ["asfreq (без изменения)"], "bfill": ["bfill (заполняет пропуски предыдущими значениями)"], "ffill": ["ffill (заполняет пропуски следующими значениями)"], "median": ["Медиана"], "Part of a Whole": ["Покомпонентное сравнение"], "Compare the same summarized metric across multiple groups.": ["Сравнивает один и тот же обобщенный показатель в нескольких группах."], "Partition Chart": ["Partition Chart"], "Categorical": ["Категориальный"], "Use Area Proportions": ["Использовать пропорции области"], "Check if the Rose Chart should use segment area instead of segment radius for proportioning": ["Проверьте, должен ли график Rose использовать площадь сегмента вместо радиуса сегмента для пропорции"], "A polar coordinate chart where the circle is broken into wedges of equal angle, and the value represented by any wedge is illustrated by its area, rather than its radius or sweep angle.": ["Полярная координатная диаграмма, где круг разбивается на клинья с равным углом, а значение, представленное любого клина, иллюстрируется его площадью, а не его радиусом или углами."], "Nightingale Rose Chart": ["Диагра<PERSON><PERSON><PERSON> Найтингейл"], "Advanced-Analytics": ["Продвинутая аналитика"], "Multi-Layers": ["Многослойный"], "Source / Target": ["Источник / Цель"], "Choose a source and a target": ["Выберите источник и цель"], "Limiting rows may result in incomplete data and misleading charts. Consider filtering or grouping source/target names instead.": ["Ограничение строк может привести к неполным данным и недостоверным графикам. Рассмотрите фильтрацию или группировку имен источника/цели."], "Visualizes the flow of different group's values through different stages of a system. New stages in the pipeline are visualized as nodes or layers. The thickness of the bars or edges represent the metric being visualized.": ["Визуализирует поток значений разных групп на разных этапах системы."], "Demographics": ["Демография"], "Survey Responses": ["Ответы на опрос"], "Sankey Diagram (legacy)": ["<PERSON><PERSON> Diagram (legacy)"], "Percentages": ["Проценты"], "Sankey Diagram with Loops": ["Схема Санки с петлями"], "Country Field Type": ["Тип поля страны"], "Full name": ["Полное имя"], "code International Olympic Committee (cioc)": ["Код Международного Олимпийского Комитета (cioc)"], "code ISO 3166-1 alpha-2 (cca2)": ["Код ISO 3166-1 alpha-2 (cca2)"], "code ISO 3166-1 alpha-3 (cca3)": ["Код ISO 3166-1 alpha-3 (cca3)"], "The country code standard that Superset should expect to find in the [country] column": ["Код страны, который Суперсет ожидает найти в столбце со страной"], "Show Bubbles": ["Показать пузыри"], "Whether to display bubbles on top of countries": ["Отображать пузыри поверх стран"], "Max Bubble Size": ["Максимальный размер пузыря"], "Color by": ["Выбор цвета по"], "Choose whether a country should be shaded by the metric, or assigned a color based on a categorical color palette": ["Выберите, должна ли страна быть затенена метрикой, или назначить цвет на основе категориальной цветовой палиты"], "Country Column": ["Столбец со страной"], "3 letter code of the country": ["3х буквенный код страны"], "Metric that defines the size of the bubble": ["Показатель, определяющий размер пузяря"], "Bubble Color": ["Цвет пузыря"], "Country Color Scheme": ["Цветовая схема страны"], "A map of the world, that can indicate values in different countries.": ["Карта мира, на которой могут быть указаны значения в разных странах."], "Multi-Dimensions": ["Многомерный"], "Multi-Variables": ["Несколько переменных"], "Featured": ["Популярные"], "deck.gl charts": ["deck.gl charts"], "Pick a set of deck.gl charts to layer on top of one another": ["Выберите набор графиков Deck.gl, чтобы слой друг на друга"], "Select charts": ["Выберите графики"], "Error while fetching charts": ["Возникла ошибка при получении графиков"], "Compose multiple layers together to form complex visuals.": ["Объединяет несколько слоев вместе для формирования сложных визуальных эффектов."], "deck.gl Multiple Layers": ["deck.gl Multiple Layers"], "deckGL": ["Карта deckGL"], "Start (Longitude, Latitude): ": ["Старт (Долгота, Широта): "], "End (Longitude, Latitude): ": ["Конец (Долгота, Широта):"], "Start Longitude & Latitude": ["Начальные долгота и широта"], "Point to your spatial columns": ["Указание на столбцы с расположением"], "End Longitude & Latitude": ["Конечные Долгота и Широта"], "Arc": ["Дуга"], "Target Color": ["Целевой цвет"], "Color of the target location": ["Цвет целевого местоположения"], "Categorical Color": ["Цвет категории"], "Pick a dimension from which categorical colors are defined": ["Выберите измерение, на основе которого определяются категориальные цвета"], "Stroke Width": ["Ши<PERSON>ина обводки"], "Advanced": ["Продвинутая настройка"], "Plot the distance (like flight paths) between origin and destination.": ["Постройте расстояние (например, пути полета) между происхождением и пунктом назначения."], "deck.gl Arc": ["deck.gl Arc"], "3D": ["3D карты"], "Web": ["Сеть"], "Centroid (Longitude and Latitude): ": ["Центроид (Долгота и Широта): "], "Threshold: ": ["Порог: "], "The size of each cell in meters": ["Размер каждой ячейки в метрах"], "Aggregation": ["Агрегация"], "The function to use when aggregating points into groups": ["функция для использования при агрегировании точек в группы"], "Contours": ["Контуры"], "Define contour layers. Isolines represent a collection of line segments that serparate the area above and below a given threshold. Isobands represent a collection of polygons that fill the are containing values in a given threshold range.": ["определить контурные слои."], "Weight": ["<PERSON>е<PERSON>"], "Metric used as a weight for the grid's coloring": ["Мера, используемая как вес для раскрашивания сетки"], "Uses Gaussian Kernel Density Estimation to visualize spatial distribution of data": ["Использует оценку плотности ядра Гаусса для визуализации пространственного распределения данных"], "deck.gl Contour": ["deck.gl <PERSON>tour"], "Spatial": ["Пространственный"], "GeoJson Settings": ["Настройки GeoJson"], "Line width unit": ["Единица измерения ширины линии"], "meters": ["метры"], "pixels": ["пиксели"], "Point Radius Scale": ["Шкала радиуса маркера"], "The GeoJsonLayer takes in GeoJSON formatted data and renders it as interactive polygons, lines and points (circles, icons and/or texts).": ["GeoJsonLayer принимает данные в формате GeoJSON и отображает их в виде интерактивных  многоугольников, линий и точек (кругов, значков и/или текстов)."], "deck.gl Geojson": ["deck.gl GeoJSON"], "Longitude and Latitude": ["Долгота и Широта"], "Height": ["Высота"], "Metric used to control height": ["Мера, используемая для регулирования высоты"], "Visualize geospatial data like 3D buildings, landscapes, or objects in grid view.": ["Визуализируйте геопространственные данные, такие как 3D здания, ландшафты или объекты в сетке.Визуализирует геопространственные данные, такие как 3D-здания, ландшафты или объекты в виде сетки."], "deck.gl Grid": ["deck.gl Grid"], "Intesity": ["Назначение"], "Intensity is the value multiplied by the weight to obtain the final weight": ["Интенсивность - это значение, умноженное на вес, чтобы получить конечный вес"], "Intensity Radius": ["Радиус интенсивности"], "Intensity Radius is the radius at which the weight is distributed": ["Радиус интенсивности - это радиус, при котором вес распределяется"], "deck.gl Heatmap": ["deck.gl Heatmap"], "Dynamic Aggregation Function": ["Динамическая агрегирующая функция"], "variance": ["Дисперсия"], "deviation": ["отклонение"], "p1": ["p1"], "p5": ["p5"], "p95": ["p95"], "p99": ["p99"], "Overlays a hexagonal grid on a map, and aggregates data within the boundary of each cell.": ["Накладывает гексагональную сетку на карту и агрегирует данные в границах каждой ячейки."], "deck.gl 3D Hexagon": ["deck.gl 3D Hexagon"], "Polyline": ["Полилина"], "Visualizes connected points, which form a path, on a map.": ["Визуализирует связанные точки, которые образуют путь, на карте."], "deck.gl Path": ["deck.gl Path"], "name": ["имя"], "Polygon Column": ["Полигонный столб"], "Polygon Encoding": ["Кодирование многоугольника"], "Elevation": ["Высота"], "Polygon Settings": ["Настройки полигона"], "Opacity, expects values between 0 and 100": ["Непрозрачность, принимаются значения от 0 до 100"], "Number of buckets to group data": ["Количество ведер для групповых данных"], "How many buckets should the data be grouped in.": ["Сколько ведер должны быть сгруппированы данные."], "Bucket break points": ["Точки разрыва бакета"], "List of n+1 values for bucketing metric into n buckets.": ["Список значений n 1 для балансирования метрики в N -ведра."], "Emit Filter Events": ["Выдать события фильтрации"], "Whether to apply filter when items are clicked": ["Применять фильтр при щелчке по элементам"], "Multiple filtering": ["Множественная фильтрация"], "Allow sending multiple polygons as a filter event": ["Возможность отправки нескольких полигонов в качестве события фильтрации"], "Visualizes geographic areas from your data as polygons on a Mapbox rendered map. Polygons can be colored using a metric.": ["Визуализирует географические области из ваших данных в виде полигонов на карте, отрисованной Mapbox. Полигоны можно раскрасить с помощью метрики."], "deck.gl Polygon": ["deck.gl Polygon"], "Category": ["Категория"], "Point Size": ["Размер маркера"], "Point Unit": ["Единица измерения маркера"], "Square meters": ["Квадратные метры"], "Square kilometers": ["Квадратные километры"], "Square miles": ["Квадратные мили"], "Radius in meters": ["Радиус в метрах"], "Radius in kilometers": ["Радиус в километрах"], "Radius in miles": ["Радиус в милях"], "Minimum Radius": ["Минимальный радиус"], "Minimum radius size of the circle, in pixels. As the zoom level changes, this insures that the circle respects this minimum radius.": ["Минимальный размер радиуса окружности (в пикселях). При изменении масштаба это гарантирует, что окружность соответствует этому минимальному радиусу."], "Maximum Radius": ["Максимальный радиус"], "Maximum radius size of the circle, in pixels. As the zoom level changes, this insures that the circle respects this maximum radius.": ["максимальный размер радиуса круга, в пикселях."], "Point Color": ["Цвет маркера"], "A map that takes rendering circles with a variable radius at latitude/longitude coordinates": ["Карта, на которой отрисовываются круги с переменным радиусом по координатам широты/долготы"], "deck.gl Scatterplot": ["deck.gl Scatterplot"], "Grid": ["Сетка"], "Aggregates data within the boundary of grid cells and maps the aggregated values to a dynamic color scale": ["агрегаты данных в границе ячеек сетки и отображают агрегированные значения в динамическую цветовую шкалу"], "deck.gl Screen Grid": ["deck.gl Screen Grid"], "For more information about objects are in context in the scope of this function, refer to the": ["Для получения дополнительной информации об объектах находится в контексте в области этой функции, см."], " source code of Superset's sandboxed parser": [" исходный код sandboxed парсера Суперсета"], "This functionality is disabled in your environment for security reasons.": ["Эта функция отключена в вашей среде по соображениям безопасности."], "Ignore null locations": ["Игнорировать пустые локации"], "Whether to ignore locations that are null": ["Игнорировать местоположения, которые не содержат данных о расположении"], "Auto Zoom": ["Авто масштабирование"], "When checked, the map will zoom to your data after each query": ["Если отмечено, карта будет смасштабирована к вашим данным после каждого запроса"], "Select a dimension": ["Выберете измерение"], "Extra data for JS": ["Доп. данные для JS"], "List of extra columns made available in JavaScript functions": ["Список дополнительных столбцов, предоставленных в функциях JavaScript"], "JavaScript data interceptor": ["JavaScript Data Interceptor"], "Define a javascript function that receives the data array used in the visualization and is expected to return a modified version of that array. This can be used to alter properties of the data, filter, or enrich the array.": ["Определите функцию JavaScript, которая получает массив данных, используемый в визуализации, и, как ожидается, вернет модифицированную версию этого массива. Определите функцию javascript, которая получает массив данных, используемый в визуализации, и, как ожидается, вернет измененную версию этого массива. Это может быть использовано для изменения свойств данных, фильтрации или расширения массива."], "JavaScript tooltip generator": ["JavaScript Tooltip Generator"], "Define a function that receives the input and outputs the content for a tooltip": ["Задайте функцию, которая получает на вход содержимое всплывающей подсказки"], "JavaScript onClick href": ["JavaScript onclick href"], "Define a function that returns a URL to navigate to when user clicks": ["Задайте функцию, которая возвращает URL для навигации при пользовательском нажатии"], "Legend Format": ["Формат легенды"], "Choose the format for legend values": ["Выберите формат значений легенды"], "Legend Position": ["Расположение легенды"], "Choose the position of the legend": ["Выберите позицию легенды"], "Top left": ["Сверху слева"], "Top right": ["Сверху справа"], "Bottom left": ["Снизу слева"], "Bottom right": ["Снизу справа"], "Lines column": ["Линии столбца"], "The database columns that contains lines information": ["Столбцы базы данных, содержащие информацию о строках"], "Line width": ["Толщина линии"], "The width of the lines": ["<PERSON><PERSON><PERSON><PERSON><PERSON> линий"], "Fill Color": ["Цвет заливки"], " Set the opacity to 0 if you do not want to override the color specified in the GeoJSON": [" Установите прозрачность 0, если вы не хотите переписывать цвет, указанный в GeoJSON"], "Stroke Color": ["Цвет обводки"], "Filled": ["С заливкой"], "Whether to fill the objects": ["Использовать заливку для объектов"], "Stroked": ["С обводкой"], "Whether to display the stroke": ["Отображение обводки"], "Extruded": ["Экструдирован"], "Whether to make the grid 3D": ["Сделать сетку 3D"], "Grid Size": ["Размер сетки"], "Defines the grid size in pixels": ["Определяет размер сетки (в пикселях)"], "Parameters related to the view and perspective on the map": ["Параметры, связанные с представлением и перспективой на карте"], "Longitude & Latitude": ["Долгота и Широта"], "Fixed point radius": ["Фиксированный радиус"], "Multiplier": ["Мультипликатор"], "Factor to multiply the metric by": ["Чи<PERSON><PERSON><PERSON>, на которое умножается мера"], "Lines encoding": ["Кодирование линий"], "The encoding format of the lines": ["Формат кодирования линий"], "geohash (square)": ["geohash (квадрат)"], "Reverse Lat & Long": ["Поменять местами широту и долготу"], "GeoJson Column": ["Столбец GeoJson"], "Select the geojson column": ["Выберите g<PERSON><PERSON> столбец"], "Right Axis Format": ["Формат правой оси"], "Show Markers": ["Показать маркеры"], "Show data points as circle markers on the lines": ["Покажите точки данных как маркеры кружков на линии"], "Y bounds": ["Показывать границы оси Y"], "Whether to display the min and max values of the Y-axis": ["Отображать минимальное и максимальное значение на оси Y"], "Y 2 bounds": ["Границы оси Y 2"], "Line Style": ["Тип линии"], "linear": ["линейный"], "basis": ["Основа"], "cardinal": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "monotone": ["монотон"], "step-before": ["шаг перед"], "step-after": ["шаг-после"], "Line interpolation as defined by d3.js": ["Линейная интерполяция, определенная в d3.js"], "Show Range Filter": ["Показать фильтр Диапазон"], "Whether to display the time range interactive selector": ["Отображение интерактивного селектора временного интервала"], "Extra Controls": ["Дополнительные элементы управления"], "Whether to show extra controls or not. Extra controls include things like making mulitBar charts stacked or side by side.": ["Отображает дополнительные элементы управления на самом графике и позволяет менять отображение столбцов: без накопления и с ним."], "X Tick Layout": ["Расположение делений оси X"], "flat": ["плоская"], "staggered": ["ступенчатый"], "The way the ticks are laid out on the X-axis": ["Способ расположения делений по оси X"], "X Axis Format": ["Формат оси X"], "Y Log Scale": ["Логарифмическая ось Y"], "Use a log scale for the Y-axis": ["Использовать логарифмическую шкалу для оси Y"], "Y Axis Bounds": ["Границы оси Y"], "Bounds for the Y-axis. When left empty, the bounds are dynamically defined based on the min/max of the data. Note that this feature will only expand the axis range. It won't narrow the data's extent.": ["Границы для оси Y. Границы для оси Y. Если оставить поле пустым, границы динамически определяются на основе минимального/максимального значения данных. Обратите внимание, что эта функция только расширит диапазон осей. Она не изменит размер графика."], "Y Axis 2 Bounds": ["Границы оси Y 2"], "X bounds": ["Показывать границы оси X"], "Whether to display the min and max values of the X-axis": ["Отображать минимальное и максимальное значение на оси X"], "Bar Values": ["Значения столбцов"], "Show the value on top of the bar": ["Показать значение в верхней части столбца"], "Stacked Bars": ["Столбцы с накоплением"], "Reduce X ticks": ["Уменьшить кол-во делений оси X"], "Reduces the number of X-axis ticks to be rendered. If true, the x-axis will not overflow and labels may be missing. If false, a minimum width will be applied to columns and the width may overflow into an horizontal scroll.": ["Уменьшает количество отрисованных делений на оси X. Если флажок установлен, некоторые метки могут быть не отображены."], "You cannot use 45° tick layout along with the time range filter": ["Вы не можете использовать расположение делений под углом 45° при использовании временного фильтра"], "Stacked Style": ["Стиль укладки"], "stack": ["стек"], "stream": ["поток"], "expand": ["развернуть"], "Evolution": ["Динамика"], "A time series chart that visualizes how a related metric from multiple groups vary over time. Each group is visualized using a different color.": ["Диаграмма временного ряда, которая визуализируе<PERSON>, как связанная метрика из нескольких групп изменяется с течением времени. Для каждой группы используется свой цвет."], "Stretched style": ["Растянутый стиль"], "Stacked style": ["Сложный стиль"], "Video game consoles": ["Игровые приставки"], "Vehicle Types": ["Типы транспортных средств"], "Time-series Area Chart (legacy)": ["Time-series Area Chart (legacy)"], "Continuous": ["Непрерывный"], "Line": ["Линейный"], "nvd3": ["Графики nvd3"], "Series Limit Sort By": ["Сортировка категорий по"], "Metric used to order the limit if a series limit is present. If undefined reverts to the first metric (where appropriate).": ["Метрика, используемая для упорядочения предела, если присутствует предел последовательности."], "Series Limit Sort Descending": ["Предельная сортировка серии по убыванию"], "Whether to sort descending or ascending if a series limit is present": ["Сортировка по убыванию или по возрастанию, если есть ограничение на количество категорий"], "Visualize how a metric changes over time using bars. Add a group by column to visualize group level metrics and how they change over time.": ["Визуализирует изменение меры с течением времени, используя столбцы. Добавьте столбец для группировки, чтобы визуализировать показатели уровня группы и то, как они меняются с течением времени."], "Time-series Bar Chart (legacy)": ["Столбчатая диаграмма (устарело)"], "Bar": ["Столбчатая"], "Box Plot": ["Box Plot"], "X Log Scale": ["Логарифмическая ось X"], "Use a log scale for the X-axis": ["Использовать логарифмическую шкалу для оси X"], "Visualizes a metric across three dimensions of data in a single chart (X axis, Y axis, and bubble size). Bubbles from the same group can be showcased using bubble color.": ["Визуализирует метрику в трех измерениях данных на одном графике (ось x, ось Y и размер пузырьков)."], "Bubble Chart (legacy)": ["Таблица пузырьков (наследие)"], "Ranges": ["Диапазоны"], "Ranges to highlight with shading": ["Диапазоны для выделения с помощью затенения"], "Range labels": ["Метки диапазона"], "Labels for the ranges": ["Метки для диапазонов"], "Markers": ["Маркеры"], "List of values to mark with triangles": ["Список числовых значений для отображения в виде треугольников на графике. Например, 10,20,30"], "Marker labels": ["Метки маркера"], "Labels for the markers": ["Метки для маркеров"], "Marker lines": ["<PERSON>и<PERSON><PERSON><PERSON> маркеров"], "List of values to mark with lines": ["Список числовых значений для отображения в виде линий на графике. Например, 10,20,30"], "Marker line labels": ["Метки линий маркера"], "Labels for the marker lines": ["Метки для линий маркера"], "KPI": ["KPI"], "Showcases the progress of a single metric against a given target. The higher the fill, the closer the metric is to the target.": ["Демонстрирует прогресс одного показателя по отношению к заданной цели. Чем больше заполнение, тем ближе показатель к целевому показателю."], "Visualizes many different time-series objects in a single chart. This chart is being deprecated and we recommend using the Time-series Chart instead.": ["Визуализирует множество разных объектов временных рядов в одной диаграмме."], "Time-series Percent Change": ["Процентное изменение (временные ряды)"], "Sort Bars": ["Сортировать столбцы"], "Sort bars by x labels.": ["Сортировать столбцы по меткам на оси X."], "Breakdowns": ["Сбои"], "Defines how each series is broken down": ["Определяет разложение каждой категории"], "Compares metrics from different categories using bars. Bar lengths are used to indicate the magnitude of each value and color is used to differentiate groups.": ["Сравнивает метрики из разных категорий с помощью столбиков. Д<PERSON>ина столбиков используется для обозначения величины каждого значения, а цвет - для различения групп."], "Bar Chart (legacy)": ["Столбчатая диаграмма (устарело)"], "Additive": ["Смешанный"], "Propagate": ["Распространение"], "Send range filter events to other charts": ["Отправить события фильтра диапазона в другие графики"], "Classic chart that visualizes how metrics change over time.": ["Классический график для визуализации изменения показателей со временем."], "Battery level over time": ["Уровень заряда батареи с течением времени"], "Time-series Line Chart (legacy)": ["Линейная диаграмма временных рядов (Legacy)"], "Label Type": ["Тип метки"], "Category Name": ["Имя категории"], "Value": ["Значение"], "Percentage": ["Процентная доля"], "Category and Value": ["Категория и значение"], "Category and Percentage": ["Категория и процентная доля"], "Category, Value and Percentage": ["Категория, значение и процентная доля"], "What should be shown on the label?": ["Текст, отображаемый на метке?"], "Donut": ["Кольцевая диаграмма"], "Do you want a donut or a pie?": ["Вы хотите пончик или пирог?"], "Show Labels": ["Показывать метки"], "Whether to display the labels. Note that the label only displays when the 5% threshold.": ["Отображать этикетки."], "Put labels outside": ["Вынести метки наружу"], "Put the labels outside the pie?": ["Вынести метки за пределы графика"], "Pie Chart (legacy)": ["Круговая диаграмма (наследие)"], "Frequency": ["Частота"], "Year (freq=AS)": ["Год (част=AS)"], "52 weeks starting Monday (freq=52W-MON)": ["52 недели с началом в Понедельник (част=52W-MON)"], "1 week starting Sunday (freq=W-SUN)": ["1 неделя с началом в Воскресенье (част=W-SUN)"], "1 week starting Monday (freq=W-MON)": ["1 неделя с началом в Понедельник (част=W-MON)"], "Day (freq=D)": ["День (част=D)"], "4 weeks (freq=4W-MON)": ["4 недели (част=4W-MON)"], "The periodicity over which to pivot time. Users can provide\n            \"Pandas\" offset alias.\n            Click on the info bubble for more details on accepted \"freq\" expressions.": ["Периодичность для группировки по времени. Пользователи могут задавать собственную частоту. Для этого нажмите на иконку с информацией."], "Time-series Period Pivot": ["Time-series Period Pivot"], "Formula": ["Формула"], "Event": ["Событие"], "Interval": ["Интервал"], "Stack": ["<PERSON><PERSON>"], "Stream": ["Поток"], "Expand": ["Ра<PERSON><PERSON><PERSON><PERSON>ить"], "Show legend": ["Показывать легенду"], "Whether to display a legend for the chart": ["Отображать легенду для графика"], "Margin": ["Отступ"], "Additional padding for legend.": ["Дополнительный отступ для легенды."], "Scroll": ["Прокрутка"], "Plain": ["Отобразить все"], "Legend type": ["Тип легенды"], "Orientation": ["Ориентация"], "Bottom": ["Снизу"], "Right": ["Справа"], "Legend Orientation": ["Ориентация легенды"], "Show Value": ["Показать значение"], "Show series values on the chart": ["Показать значения категорий на графике"], "Stack series on top of each other": ["Совместить столбцы в один с накоплением"], "Only Total": ["Только общий итог"], "Only show the total value on the stacked chart, and not show on the selected category": ["Показывать только общий итог для столбцов с накоплением, и не показывать промежуточные итоги для каждой категории внутри столбца."], "Percentage threshold": ["Процентный порог"], "Minimum threshold in percentage points for showing labels.": ["Минимальный порог в процентных пунктах для отображения меток."], "Rich tooltip": ["Расширенная всплывающая подсказка"], "Shows a list of all series available at that point in time": ["Показывает список всех данных, доступных в определенный момент времени"], "Tooltip time format": ["Формат времени всплывающей подсказки"], "Tooltip sort by metric": ["Сортировка данных подсказки по мере"], "Whether to sort tooltip by the selected metric in descending order.": ["Сортировка выбранных мер по убыванию во всплывающей подсказке."], "Show total": ["Показывать всего"], "Whether to display the total value in the tooltip": ["Отображать общее значение в подсказке"], "Whether to display the percentage value in the tooltip": ["Отображать процентное значение в подъеме инструментов"], "Tooltip": ["Всплывающая подсказка"], "Sort Series By": ["Сортировать серии по"], "Based on what should series be ordered on the chart and legend": ["Исходя из того, что следует заказать серии на графике и легенде"], "Sort Series Ascending": ["Сортировка серии по возрастанию"], "Sort series in ascending order": ["Сортировки серий в порядке возрастания"], "Rotate x axis label": ["Повернуть метку оси X"], "Input field supports custom rotation. e.g. 30 for 30°": ["Поле для ввода поддерживает пользовательские значения, например 30 для 30°"], "Series Order": ["Порядок серий"], "Truncate X Axis": ["Усеченная ось X"], "Truncate X Axis. Can be overridden by specifying a min or max bound. Only applicable for numercal X axis.": ["Усечение оси X. Можно переопределить, указав минимальное или максимальное ограничение. Применяется только для числовой оси X."], "X Axis Bounds": ["Границы по оси X"], "Bounds for numerical X axis. Not applicable for temporal or categorical axes. When left empty, the bounds are dynamically defined based on the min/max of the data. Note that this feature will only expand the axis range. It won't narrow the data's extent.": ["Границы для численной оси x."], "Minor ticks": ["Второстепенные засечки"], "Show minor ticks on axes.": ["Показать незначительные клещи на топорах."], "Make the x-axis categorical": ["Сделайте категорию оси X"], "Last available value seen on %s": ["Последнее доступное значение: %s"], "Not up to date": ["Не актуально"], "No data after filtering or data is NULL for the latest time record": ["Нет данных после фильтрации или данные отсутствуют за последний отрезок времени"], "Try applying different filters or ensuring your datasource has data": ["Попробуйте использовать другие фильтры или убедитесь, что в вашем источнике данных есть данные"], "Big Number Font Size": ["Размер шрифта числа"], "Tiny": ["Крошечный"], "Small": ["Маленький"], "Normal": ["Обычный"], "Large": ["Больш<PERSON>й"], "Huge": ["Огромный"], "Subheader Font Size": ["Размер шрифта подзаголовка"], "Default": ["По умолчанию"], "Data for %s": ["Данные для %s"], "Value difference between the time periods": ["Разница в значениях между периодами времени"], "Percentage difference between the time periods": ["Процентная разница между периодами времени"], "Percent Difference format": ["Процентный формат различий"], "Comparison font size": ["Сравнение размер шрифта"], "Add color for positive/negative change": ["Добавить цвет для положительного/отрицательного изменений"], "color scheme for comparison": ["цветовая схема для сравнения"], "Green for increase, red for decrease": ["Зеленый для увеличения, красный для уменьшения"], "Red for increase, green for decrease": ["красный для увеличения, зеленый для уменьшения"], "Adds color to the chart symbols based on the positive or negative change from the comparison value.": ["Добавляет цвет к символам графика на основе положительного или отрицательного изменения от значения сравнения."], "Big Number with Time Period Comparison": ["Карточка со сравнением временных периодов"], "Display settings": ["Настройки отображения"], "Subheader": ["Подзаголовок"], "Description text that shows up below your Big Number": ["Описание, отображаемое под Карточкой"], "Date format": ["Форматы даты"], "Force date format": ["Принудительный перевод к формату дата/время"], "Use date formatting even when metric value is not a timestamp": ["Использовать перевод к формату дата/время даже если мера представляет другой тип данных"], "Export as time": ["Экспортировать как величину времени"], "Export a numeric value as number of days": ["Экспорт числового значения в виде количества дней"], "Showcases a single metric front-and-center. Big number is best used to call attention to a KPI or the one thing you want your audience to focus on.": ["Отображает один показатель по центру. Карточку лучше всего использовать, чтобы привлечь внимание к KPI."], "A Big Number": ["Карточка"], "With a subheader": ["С подзаголовком"], "Big Number": ["Карточка"], "Comparison Period Lag": ["Временной лаг для сравнения"], "Based on granularity, number of time periods to compare against": ["Основываясь на группировке времени, количество периодов времени для сравнения"], "Comparison suffix": ["Текст рядом с процентным изменением"], "Suffix to apply after the percentage display": ["Текст после отображения процентной доли"], "Show Timestamp": ["Показать метку времени"], "Whether to display the timestamp": ["Отображение временную метку"], "Show Trend Line": ["Показать трендовую линию"], "Whether to display the trend line": ["Отображение трендовой линии"], "Start y-axis at 0": ["Начать ось Y с 0"], "Start y-axis at zero. Uncheck to start y-axis at minimum value in the data.": ["Начать ось Y в нуле. Снимите флаг, чтобы ось Y начиналась на минимальном значении данных"], "Fix to selected Time Range": ["Выбрать временной интервал"], "Fix the trend line to the full time range specified in case filtered results do not include the start or end dates": ["Фиксирует линию тренда в полном временном интервале, указанном в случае, если отфильтрованные результаты не включают даты начала или окончания"], "TEMPORAL X-AXIS": ["ВРЕМЕННАЯ ОСЬ X"], "Showcases a single number accompanied by a simple line chart, to call attention to an important metric along with its change over time or other dimension.": ["Отображает один показатель, сопровождаемый простой линейной диаграммой, чтобы привлечь внимание к KPI наряду с его изменением с течением времени или другим измерением."], "Big Number with Trendline": ["Карточка с трендовой линией"], "N/A": ["Пусто"], "Whisker/outlier options": ["Настройки усов/выбросов"], "Determines how whiskers and outliers are calculated.": ["Определяет формулу расчета \"усов\" и выбросов."], "Tukey": ["<PERSON><PERSON>"], "Min/max (no outliers)": ["Мин/макс (без выбросов)"], "2/98 percentiles": ["2/98 перцентели"], "9/91 percentiles": ["9/91 перцентели"], "Categories to group by on the x-axis.": ["Категории для группировки по оси x"], "Distribute across": ["Распространять через"], "Columns to calculate distribution across.": ["Столбцы для расчета распределения по всему."], "Also known as a box and whisker plot, this visualization compares the distributions of a related metric across multiple groups. The box in the middle emphasizes the mean, median, and inner 2 quartiles. The whiskers around each box visualize the min, max, range, and outer 2 quartiles.": ["Также известный как график коробки и визита, эта визуализация сравнивает распределения связанной метрики по нескольким группам."], "ECharts": ["Графики Apache"], "Min Bubble Size": ["Минимальный размер пузыря"], "Bubble size number format": ["Числовой формат размера пузырьков"], "Bubble Opacity": ["Пузырьковая непрозрачность"], "Opacity of bubbles, 0 means completely transparent, 1 means opaque": ["Непрозрачность пузырьков, 0 означает полностью прозрачный, 1 означает непрозрачный"], "X AXIS TITLE MARGIN": ["ОТСТУП ОТ ЗАГОЛОВКА ОСИ X"], "Logarithmic x-axis": ["Логарифмическая ось X"], "Rotate y axis label": ["Повернуть метку оси y"], "Y AXIS TITLE MARGIN": ["Отступ названия оси Y"], "Logarithmic y-axis": ["Логарифмическая ось Y"], "Truncate Y Axis": ["Усеченная ось Y"], "Truncate Y Axis. Can be overridden by specifying a min or max bound.": ["Уменьшить интервал по умолчанию для оси Y. Необходимо задать минимальную и максимальную границы. Обратите внимание, что некоторые линии могут пропасть из области видимости."], "Values align": ["значения выравниваются"], "Whether to sort descending or ascending": ["Сортировка по убыванию или по возрастанию"], "Show values total": ["Показать значения Всего"], "Show total value for the chart without hovering": ["Показать общее значение для диаграммы без пари"], "Show values separately": ["Показать значения отдельно"], "Show values for the chart without hovering": ["Показывать значения для графика без наведения курсора"], "Stack series": ["Использовать накопление"], "Show zoom controls": ["Показать элементы управления Zoom"], "Data Zoom Y": ["Данные Zoom y"], "Enable data zooming controls (Y)": ["Включить элементы управления масштабированием данных (Y)"], "Data Zoom X": ["Данные Zoom x"], "Enable data zooming controls (X)": ["Включить элементы управления масштабированием данных (x)"], "Echarts Bar Chart": ["Echarts Bar Chart"], "Popular": ["Популярно"], "DODOIS_friendly": ["DODOIS_friendly"], "Conditional Formatting": ["Условное форматирование"], "Apply conditional color formatting to metric": ["Применить условное форматирование цвета к метрике"], "Conditional formatting message": ["Сообщение условного форматирования"], "Show conditional color formatting message": ["Показать сообщение о форматировании условного цвета"], "Chart description": ["Описание графика"], "Tooltip text that shows up below this chart on dashboard": ["Подсказка, которая отображается ниже этого графика на дашборде"], "Conditional message Font Size": ["Условный размер шрифта сообщения"], "Alignment": ["Выравнивание"], "Value to show": ["Отображаемое значение"], "Conditional formatting": ["Условное форматирование"], "Comparison period conditional Formatting": ["условное форматирование периода сравнения"], "Apply comporation period conditional color formatting": ["Применить период кондиционирования."], "Use a log scale for the X-axis.": ["Использовать логарифмическую шкалу для оси X"], "X Axis name": ["Метка оси X"], "X axis name": ["Метка оси X"], "Name location": ["Положение метки"], "Name gap (in pixels)": ["Отступ метки (в пикселях)"], "Name gap from chart grid": ["Отступ метки от сетки графика"], "Use a log scale for the Y-axis.": ["Использовать логарифмическую шкалу для оси Y"], "Y Axis name": ["Метка оси Y"], "Y axis name": ["Метка оси Y"], "Dimension Options": ["Настройка измерений"], "Show dimension": ["Показать измерения"], "Whether to display the dimension.": ["Отображение измерения или нет"], "Grid margin top (in pixels)": ["Отступ сетки сверху (в пикселях)"], "Margin top for chart grid": ["Верхний отступ для сетки графика"], "Scroll dimension": ["Прокручивать измерения"], "Whether to scroll dimensions.": ["Прокручивать измерения bkb ytn"], "Label Options": ["Настройка меток"], "Whether to display the labels.": ["Отображать метки"], "Label location": ["Положение метки"], "Label font (in pixels)": ["Размер грифта меток (в пискселях)"], "Label color": ["Цвет меток"], "Size": ["Размер"], "% calculation": ["% расчет"], "Display percents in the label and tooltip as the percent of the total value, from the first step of the funnel, or from the previous step in the funnel.": ["Отображение каких -либо викцентов на этикетке и всплеске инструментов как процент от общей стоимости, с первого этапа воронки или с предыдущего шага в воронке."], "Calculate from first step": ["Рассчитайте с первого шага"], "Calculate from previous step": ["Рассчитайте с предыдущего шага"], "Percent of total": ["Процент от общего числа"], "Labels": ["Метки"], "Label Contents": ["Содержимое этикетки"], "Value and Percentage": ["Значение и процент"], "What should be shown as the label": ["Что должно быть показано как этикетка"], "Tooltip Contents": ["Содержимое подъема инструментов"], "What should be shown as the tooltip label": ["Что должно быть показано в качестве метки подъема инструментов"], "Show Tooltip Labels": ["Показать этикетки подсказки"], "Whether to display the tooltip labels.": ["Отображать ли метки подсказки."], "Showcases how a metric changes as the funnel progresses. This classic chart is useful for visualizing drop-off between stages in a pipeline or lifecycle.": ["Отображает изменение показателя по мере сужения воронки. Эта классическая диаграмма полезна для визуализации перехода между этапами процесса или жизненного цикла."], "Funnel Chart": ["Funnel Chart"], "Sequential": ["Последовательность"], "Columns to group by": ["Столбцы для группировки"], "General": ["Основные свойства"], "Min": ["Мини<PERSON>ум"], "Minimum value on the gauge axis": ["Минимальное значение индикатора"], "Max": ["Максимум"], "Maximum value on the gauge axis": ["Максимальное значение индикатора"], "Start angle": ["Начальный угол"], "Angle at which to start progress axis": ["Угол, с которого начинается ось прогресса"], "End angle": ["Конечный угол"], "Angle at which to end progress axis": ["Угол, под которым заканчивается ось прогресса"], "Font size": ["Размер шрифта"], "Font size for axis labels, detail value and other text elements": ["Размер шрифта для меток осей, значений деталей и других текстовых элементов"], "Value format": ["Формат значения"], "Additional text to add before or after the value, e.g. unit": ["Дополнительный текст перед значением, например, единица измерения"], "Show pointer": ["Показывать указатель"], "Whether to show the pointer": ["Отображение указателя"], "Animation": ["Анимация"], "Whether to animate the progress and the value or just display them": ["Анимировать прогресс и значение или просто отображать их"], "Axis": ["Ось"], "Show axis line ticks": ["Показывать деления на оси"], "Whether to show minor ticks on the axis": ["Отображение мелких отметок на оси"], "Show split lines": ["Показывать разделительные линии"], "Whether to show the split lines on the axis": ["Отображение линий разделения на оси"], "Split number": ["Количество разделителей"], "Number of split segments on the axis": ["Количество разделенных сегментов на индикаторе"], "Progress": ["Прогресс"], "Show progress": ["Показывать прогресс"], "Whether to show the progress of gauge chart": ["Нужно ли показывать ход выполнения графика gauge"], "Overlap": ["Перекрывание"], "Whether the progress bar overlaps when there are multiple groups of data": ["Индикатор прогресса накладывается при наличии нескольких групп данных"], "Round cap": ["Закругление на концах"], "Style the ends of the progress bar with a round cap": ["Оформление концов индикатора круглыми заглушками"], "Intervals": ["Интервалы"], "Interval bounds": ["Граница интервала"], "Comma-separated interval bounds, e.g. 2,4,5 for intervals 0-2, 2-4 and 4-5. Last number should match the value provided for MAX.": ["Границы интервала, разделенные запятой, например, 2,4,5 для интерв<PERSON>лов 0-2, 2-4 и 4-5. Последнее число должно быть равно заданному максиму."], "Interval colors": ["Цвета интервала"], "Comma-separated color picks for the intervals, e.g. 1,2,4. Integers denote colors from the chosen color scheme and are 1-indexed. Length must be matching that of interval bounds.": ["Номера цветов, разделенные запятой, например, 1,2,4. Целые числа задают цвета из выбранной цветовой схемы и начинаются с 1 (не с нуля). Длина должна соответствовать границам интервала."], "Uses a gauge to showcase progress of a metric towards a target. The position of the dial represents the progress and the terminal value in the gauge represents the target value.": ["Использует индикатор для демонстрации прогресса показателя в достижении цели. Положение циферблата показывает ход выполнения, а конечное значение на индикаторе представляет целевое значение."], "Gauge Chart": ["Индикаторная диаграмма"], "Name of the source nodes": ["Имя исходных вершин"], "Name of the target nodes": ["Имя конечных вершин"], "Source category": ["Исходная категория"], "The category of source nodes used to assign colors. If a node is associated with more than one category, only the first will be used.": ["Категория исходных вершин предназначена для задания цветов. Если вершина связана более, чем с одной категорией, только первая будет использована."], "Target category": ["Целевая категория"], "Category of target nodes": ["Категория целевых вершин"], "Chart options": ["Свойства графика"], "Layout": ["Оформление"], "Graph layout": ["Формат сетевого графика"], "Force": ["Силовой алгоритм"], "Layout type of graph": ["Тип макета графика"], "Edge symbols": ["Оформление ребер"], "Symbol of two ends of edge line": ["Символ двух концов края линии"], "None -> None": ["Ничего -> Ничего"], "None -> Arrow": ["Ничего -> Стрелка"], "Circle -> Arrow": ["Круг -> Стрелка"], "Circle -> Circle": ["Круг -> Круг"], "Enable node dragging": ["Разрешить перемещение вершин"], "Whether to enable node dragging in force layout mode.": ["Включить перемещение вершин в режиме силового алгоритма."], "Enable graph roaming": ["Включить перемещение по графику"], "Disabled": ["Отключено"], "Scale only": ["Только масштабирование"], "Move only": ["Только перемещение"], "Scale and Move": ["Масштабирование и перемещение"], "Whether to enable changing graph position and scaling.": ["Чтобы включить изменение позиции графика и масштабирование."], "Node select mode": ["Режим выбора вершин"], "Single": ["<PERSON><PERSON><PERSON><PERSON>"], "Multiple": ["Несколько"], "Allow node selections": ["Разрешить выбор вершин"], "Label threshold": ["Порог метки"], "Minimum value for label to be displayed on graph.": ["Минимальное значение метки для отображения на графике."], "Node size": ["Размер вершины"], "Median node size, the largest node will be 4 times larger than the smallest": ["Медианный размер вершины, самая большая вершина будет в 4 раза больше самой маленькой."], "Edge width": ["Толщина ребра"], "Median edge width, the thickest edge will be 4 times thicker than the thinnest.": ["Медианная толщина ребра, самое толстое ребро будет в 4 раза толще самой тонкой."], "Edge length": ["<PERSON><PERSON><PERSON><PERSON> ребер"], "Edge length between nodes": ["Длина ребер между вершинами"], "Gravity": ["Гравитация"], "Strength to pull the graph toward center": ["Сила притяжения вершин к центру"], "Repulsion": ["Отталкивание"], "Repulsion strength between nodes": ["Сила отталкивания вершин"], "Friction": ["Трение"], "Friction between nodes": ["Сила трения между вершинами"], "Displays connections between entities in a graph structure. Useful for mapping relationships and showing which nodes are important in a network. Graph charts can be configured to be force-directed or circulate. If your data has a geospatial component, try the deck.gl Arc chart.": ["Отображает соединения между объектами в структуре графика."], "Graph Chart": ["Сетевой график"], "Structural": ["Структура"], "Legend Type": ["Тип легенды"], "Piecewise": ["Кусочно"], "Hard value bounds applied for color coding.": ["Границы жестких значений применяются для цветовой кодировки."], "Y-Axis": ["ось Y"], "Numeric column used to calculate the histogram.": ["Числовой столбец, используемый для расчета гистограммы."], "Bins": ["Bins"], "The number of bins for the histogram": ["Количество бункеров для гистограммы"], "Normalize": ["Нормализовать"], "\n                The normalize option transforms the histogram values into proportions or\n                probabilities by dividing each bin's count by the total count of data points.\n                This normalization process ensures that the resulting values sum up to 1,\n                enabling a relative comparison of the data's distribution and providing a\n                clearer understanding of the proportion of data points within each bin.": ["\n                Опция «Нормализовать» преобразует значения гистограммы в пропорции или\n                вероятности, деляя количество каждого бина на общее количество точек данных.\n                Этот процесс нормализации гарантирует, что полученные значения подводят 1, что \n                позволяет относительно сравнительно сравнительно сравнивать распределение данных и обеспечивающих более ясное понимание доли данных данных.\n                Нормировка преобразует значения гистограммы в пропорции или \n                вероятности путем деления количества точек в каждом бине на общее количество точек данных. \n                Такой процесс гарантирует, что итоговые суммы будут равны 1, \n                что позволяет сравнить распределение данных и получить более четкое \n                представление о доле точек данных в каждом бине."], "\n                The cumulative option allows you to see how your data accumulates over different\n                values. When enabled, the histogram bars represent the running total of frequencies\n                up to each bin. This helps you understand how likely it is to encounter values\n                below a certain point. Keep in mind that enabling cumulative doesn't change your\n                original data, it just changes the way the histogram is displayed.": ["\n                Совокупный параметр позволяет вам увидеть, как ваши данные накапливаются по разным значениям. \n                Опция позволяет увидеть, как накапливаются данные при различных значениях. \n                Если включено, столбцы гистограммы представляют собой общую сумму частот до \n                каждого бина. Это поможет вам понять, насколько велика вероятность встретить \n                значения ниже определенной точки. Помните, что включение этого режима не изменяет \n                исходные данные, а лишь меняет способ отображения гистограммы."], "The histogram chart displays the distribution of a dataset by\n          representing the frequency or count of values within different ranges or bins.\n          It helps visualize patterns, clusters, and outliers in the data and provides\n          insights into its shape, central tendency, and spread.": ["График Гистограмма отображает распределение набора данных путем,\n          представляющего частоту или подсчет значений в разных диапазонах или ящиках.\n          Он помогает визуализировать закономерности, кластеры и выбросы в данных и дает\n          представление о его форме, центральной тенденции и разбросе."], "Show values": ["Показать значения"], "Hide values": ["Скрыть значения"], "Series type": ["Тип серии"], "Smooth Line": ["Гладкая линия"], "Step - start": ["Шаг - начало"], "Step - middle": ["шаг - середина"], "Step - end": ["шаг - конец"], "Series chart type (line, bar etc)": ["Тип диаграммы серии (линия, бар и т. Д.)"], "Area chart": ["Диаграмма с областями"], "Draw area under curves. Only applicable for line types.": ["Отобразить область под кривыми. Применимо только для линий\""], "Opacity of area chart.": ["Непрозрачность диаграммы областей."], "Marker": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "Draw a marker on data points. Only applicable for line types.": ["Отобразить маркеры на данных. Применимо только для линий."], "Marker size": ["Размер маркера"], "Size of marker. Also applies to forecast observations.": ["Размер маркера. Также применяется к прогнозным значениям."], "Primary": ["Первичная"], "Secondary": ["Вторичная"], "Primary or secondary y-axis": ["Первичная или вторичная ось Y"], "Shared query fields": ["Поля общедоступного запроса"], "Query A": ["Запрос А"], "Advanced analytics Query A": ["Расширенный анализ: запрос А"], "Query B": ["Запрос Б"], "Advanced analytics Query B": ["Расширенный анализ: запрос Б"], "Data Zoom": ["Масштабирование графика"], "Enable data zooming controls": ["Включить элементы управления масштабированием данных"], "Minor Split Line": ["Разметка полотна линиями"], "Draw split lines for minor y-axis ticks": ["Рисует разделительные линии для небольших отметок оси Y"], "Primary y-axis Bounds": ["Главные границы оси Y"], "Bounds for the primary Y-axis. When left empty, the bounds are dynamically defined based on the min/max of the data. Note that this feature will only expand the axis range. It won't narrow the data's extent.": ["Границы для первичной оси Y."], "Primary y-axis format": ["Формат первичной оси Y"], "Logarithmic scale on primary y-axis": ["Логарифмическая шкала для главной оси Y"], "Secondary y-axis Bounds": ["вторичные границы оси Y."], "Bounds for the secondary Y-axis. Only works when Independent Y-axis\n                bounds are enabled. When left empty, the bounds are dynamically defined\n                based on the min/max of the data. Note that this feature will only expand\n                the axis range. It won't narrow the data's extent.": ["Границы для вторичной оси Y."], "Secondary y-axis format": ["Формат вторичной оси Y"], "Secondary currency format": ["Формат вторичной валюты"], "Secondary y-axis title": ["Название вторичной оси Y"], "Logarithmic scale on secondary y-axis": ["Логарифмическая шкала для вторичной оси Y"], "Customize Metrics": ["Настроить меры"], "Visualize two different series using the same x-axis. Note that both series can be visualized with a different chart type (e.g. 1 using bars and 1 using a line).": ["Визуализируйте две разные серии, используя одну и ту же ось X."], "Mixed Chart": ["Смешанный график"], "Rose Type": ["Тип розы"], "Area": ["Площадь"], "Radius": ["Радиус"], "Whether to show as Nightingale chart.": ["Показывать ли диаграмму Nightingale."], "Template": ["Шабл<PERSON>н"], "Label Template": ["Шаблон метки"], "Format data labels. Use variables: {name}, {value}, {percent}. \\n represents a new line. ECharts compatibility:\n{a} (series), {b} (name), {c} (value), {d} (percentage)": ["Форматируйте метки данных. Используйте переменные: {name}, {value}, {percent}. \\n обозначает новую строку. Совместимость с ECharts:\n{a} (серия), {b} (имя), {c} (значение), {d} (процент)"], "Put the labels outside of the pie?": ["Вынести метки за пределы графика"], "Label Line": ["Линия метки"], "Draw line from Pie to label when labels outside?": ["Проводить линию от графика к метке, когда метки находятся снаружи"], "Show Total": ["Показать общий итог"], "Whether to display the aggregate count": ["Отображать совокупное количество"], "Pie shape": ["Форма круговой диаграммы"], "Outer Radius": ["Внешний радиус"], "Outer edge of Pie chart": ["Внешний радиус круговой диаграммы"], "Inner Radius": ["Внутренний радиус"], "Inner radius of donut hole": ["Внутренний радиус отверстия для кольца"], "The classic. Great for showing how much of a company each investor gets, what demographics follow your blog, or what portion of the budget goes to the military industrial complex.\n\n        Pie charts can be difficult to interpret precisely. If clarity of relative proportion is important, consider using a bar or other chart type instead.": ["Классика. Отлично подходит для того, чтобы показать, какую долю в компании получает каждый инвестор, какие демографические группы следят за вашим блогом, или какая часть бюджета идет на военно- промышленный комплекс.\n\n        Круговые графики бывает сложно точно интерпретировать. Если вам важна ясность относительная пропорция важна, подумайте об использовании столбчатых или других графиков вместо него"], "Pie Chart": ["Круговая диаграмма"], "Nightingale": ["Соловья"], "Total: %s": ["Итого: %s"], "The maximum value of metrics. It is an optional configuration": ["Максимальное значение мер. Это необязательная настройка"], "Label position": ["Положение метки"], "Radar": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Further customize how to display each metric": ["Дальнейшая настройка отображения каждой меры"], "Circle radar shape": ["Круглая форма радара"], "Radar render type, whether to display 'circle' shape.": ["Тип рендеринга радара, чтобы отображать форму «круга»."], "Visualize a parallel set of metrics across multiple groups. Each group is visualized using its own line of points and each metric is represented as an edge in the chart.": ["Визуализируйте параллельный набор метрик в нескольких группах."], "Radar Chart": ["Диаграмма радар"], "The column to be used as the source of the edge.": ["Столбец, который будет использоваться в качестве источника края."], "The column to be used as the target of the edge.": ["Столбец, который будет использоваться в качестве цели края."], "The Sankey chart visually tracks the movement and transformation of values across\n          system stages. Nodes represent stages, connected by links depicting value flow. Node\n          height corresponds to the visualized metric, providing a clear representation of\n          value distribution and transformation.": ["График Sankey визуально отслеживает движение и преобразование значений на этапах системы.\n          Узлы представляют этапы, соединенные связями, изображающими поток ценностей. Узел\n          высота узла соответствует визуализированной метрике, обеспечивая наглядное представление\n          Распределения и трансформации стоимости."], "Sankey Chart": ["<PERSON>ра<PERSON><PERSON><PERSON>"], "Primary Metric": ["Основная мера"], "The primary metric is used to define the arc segment sizes": ["Основная мера используется для определения размера сегмента дуги"], "Secondary Metric": ["Вторичная мера"], "[optional] this secondary metric is used to define the color as a ratio against the primary metric. When omitted, the color is categorical and based on labels": ["[необязательно] вторичная мера используется для определения цвета как доли по отношению к основной мере. Если не выбрано, цвет задается согласно имени категории"], "When only a primary metric is provided, a categorical color scale is used.": ["Когда предоставляется только основная мера, используется категориальная цветовая схема."], "When a secondary metric is provided, a linear color scale is used.": ["Когда предоставляется вторичная мера, используется линейная цветовая схема."], "Hierarchy": ["Иерархия"], "Sets the hierarchy levels of the chart. Each level is\n        represented by one ring with the innermost circle as the top of the hierarchy.": ["Устанавливает уровни иерархии графика. Каждый уровень\n        представлен одним кольцом с самым внутренним кругом в качестве вершины иерархии."], "Uses circles to visualize the flow of data through different stages of a system. Hover over individual paths in the visualization to understand the stages a value took. Useful for multi-stage, multi-group visualizing funnels and pipelines.": ["использует круги для визуализации потока данных через разные этапы системы."], "Sunburst Chart": ["Диаграмма Солнечные лучи"], "Multi-Levels": ["Многоуровневый"], "% of total": ["% от общего числа"], "% of parent": ["% родителей"], "When using other than adaptive formatting, labels may overlap": ["При использовании, кроме адаптивного форматирования, этикетки могут перекрываться"], "Swiss army knife for visualizing data. Choose between step, line, scatter, and bar charts. This viz type has many customization options as well.": ["швейцарский армейский нож для визуализации данных."], "Generic Chart": ["Общая диаграмма"], "zoom area": ["зомская зона"], "restore zoom": ["восстановить масштабирование"], "Series Style": ["Стиль категорий"], "Area chart opacity": ["Непрозрачность диаграммы с областями"], "Opacity of Area Chart. Also applies to confidence band.": ["Непрозрачность зоны."], "Marker Size": ["Размер маркера"], "Area charts are similar to line charts in that they represent variables with the same scale, but area charts stack the metrics on top of each other.": ["Диаграммы с областями похожи на линейные диаграммы в том смысле, что они отображают показатели с одинаковым масштабом, но диаграммы областей накладывают эти показатели друг на друга."], "Area Chart": ["Диаграмма с областями"], "Axis Title": ["Название оси"], "AXIS TITLE MARGIN": ["ОТСТУП ЗАГОЛОВКА ОСИ"], "AXIS TITLE POSITION": ["ПОЛОЖЕНИЕ ЗАГОЛОВКА ОСИ"], "Axis Format": ["Формат Оси"], "Logarithmic axis": ["Логарифмическая ось"], "Draw split lines for minor axis ticks": ["Рисует разделительные линии для небольших отметок оси"], "Truncate Axis": ["Настройка интервала оси"], "It’s not recommended to truncate axis in Bar chart.": ["Не рекомендуется урезать интервал оси в столбчатой диаграмме"], "Axis Bounds": ["Границы оси"], "Bounds for the axis. When left empty, the bounds are dynamically defined based on the min/max of the data. Note that this feature will only expand the axis range. It won't narrow the data's extent.": ["Границы для оси. Границы для оси. Если оставить поле пустым, границы динамически определяются на основе минимального/максимального значения данных. Обратите внимание, что эта функция только расширит диапазон осей. Она не изменит размер графика."], "Chart Orientation": ["Ориентация графика"], "Bar orientation": ["Направление столбцов"], "Vertical": ["Вертикально"], "Horizontal": ["Горизонтально"], "Orientation of bar chart": ["Ориентация диаграммы"], "Bar Charts are used to show metrics as a series of bars.": ["Столбчатые графики используются для отображения показателей в виде серии столбиков."], "Bar Chart": ["Столбчатая диаграмма"], "Line chart is used to visualize measurements taken over a given category. Line chart is a type of chart which displays information as a series of data points connected by straight line segments. It is a basic type of chart common in many fields.": ["Линейный график используется для визуализации измерений, взятых по данной категории. Линейный график используется для визуализации показателей, полученных в рамках одной категории. Линейный график - это тип графиков, который отображает информацию в виде ряда точек данных, соединенных прямыми отрезками. Это базовый тип диаграммы, распространенный во многих областях."], "Line Chart": ["Линейный график"], "Scatter Plot has the horizontal axis in linear units, and the points are connected in order. It shows a statistical relationship between two variables.": ["Графи<PERSON> Scatter Plot  имеет горизонтальную ось в линейных единицах, а точки соединены по порядку. Он показывает статистическую связь между двумя переменными."], "Scatter Plot": ["Точечная диаграмма"], "Smooth-line is a variation of the line chart. Without angles and hard edges, Smooth-line sometimes looks smarter and more professional.": ["Плавная линия» - это разновидность линейного графика. Без углов и жестких краев.Плавная линия иногда выглядит умнее и профессиональнее."], "Step type": ["Тип шага"], "Start": ["Начало"], "Middle": ["Середина"], "End": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Defines whether the step should appear at the beginning, middle or end between two data points": ["Определя<PERSON><PERSON>, должен ли шаг отображаться в начале, середине или конце между двумя точками данных"], "Stepped-line graph (also called step chart) is a variation of line chart but with the line forming a series of steps between data points. A step chart can be useful when you want to show the changes that occur at irregular intervals.": ["График Stepped-Line (также называемый этап-диаграмма) представляет собой вариацию линейной диаграммы, но с линией, образующей серию шагов между точками данных."], "Stepped Line": ["Stepped Line"], "Name of the id column": ["Имя столбца id"], "Parent": ["Родитель"], "Name of the column containing the id of the parent node": ["Имя столбца, содержащее id родительской вершины"], "Optional name of the data column.": ["Необязательное имя столбца данных."], "Root node id": ["Идентификатор корневого узла"], "Id of root node of the tree.": ["Id корневой вершины дерева."], "Metric for node values": ["Мера для значений вершин"], "Tree layout": ["Оформление дерева"], "Orthogonal": ["ортогональный"], "Radial": ["Радиальное"], "Layout type of tree": ["Тип макета дерева"], "Tree orientation": ["Ориентация дерева"], "Left to Right": ["Слева направо"], "Right to Left": ["Справа налево"], "Top to Bottom": ["Сверху вниз"], "Bottom to Top": ["Снизу вверх"], "Orientation of tree": ["Ориентация дерева"], "Node label position": ["Расположение метки вершины"], "left": ["слева"], "top": ["сверху"], "right": ["справа"], "bottom": ["снизу"], "Position of intermediate node label on tree": ["Положение метки промежуточного узла на дереве"], "Child label position": ["Положение метки дочернего элемента"], "Position of child node label on tree": ["Расположение метки дочерней вершины на дереве"], "Emphasis": ["Акцент"], "ancestor": ["предок"], "descendant": ["потомок"], "Which relatives to highlight on hover": ["Подсвечивается при наведении"], "Symbol": ["Символ"], "Empty circle": ["Пустой круг"], "Circle": ["Круг"], "Rectangle": ["Прямоугольник"], "Triangle": ["Треугольник"], "Diamond": ["Ромб"], "Pin": ["Закрепить"], "Arrow": ["Стрела"], "Symbol size": ["Размер символа"], "Size of edge symbols": ["Размер краевых символов"], "Visualize multiple levels of hierarchy using a familiar tree-like structure.": ["Визуализирует несколько уровней иерархии, используя древовидную структуру."], "Tree Chart": ["Древовидная диаграмма"], "Show Upper Labels": ["Показать верхние метки"], "Show labels when the node has children.": ["Показывать метки, когда у вершины есть дочерние элементы."], "Key": ["<PERSON><PERSON><PERSON><PERSON>"], "Show hierarchical relationships of data, with the value represented by area, showing proportion and contribution to the whole.": ["Покажите иерархические отношения данных, причем значение представлено площадью, показывающая пропорцию и вклад в целое."], "Treemap": ["Treemap"], "Total": ["Итого"], "Assist": ["Помощь"], "Increase": ["Увеличение"], "Decrease": ["Уменьшение"], "Series colors": ["Цвета серий"], "Breaks down the series by the category specified in this control.\n      This can help viewers understand how each category affects the overall value.": ["Разбивает серию по категории, указанной в этом элементе управления.\n      Это может помочь зрителям понять, как каждая категория влияет на общее значение."], "A waterfall chart is a form of data visualization that helps in understanding\n          the cumulative effect of sequentially introduced positive or negative values.\n          These intermediate values can either be time based or category based.": ["Диаграмма водопада - это форма визуализации данных, которая помогает в понимании совокупного эффекта последовательно введенных положительных или отрицательных значений."], "Waterfall Chart": ["Waterfall Chart"], "page_size.all": ["все"], "Content with navigation": ["Содержимое с навигацией"], "Reset view (Esc)": ["Сбросить вид (Esc)"], "Reset": ["Сбросить"], "Double-click to reset zoom. Use +/- keys to zoom in/out": ["Двойной щелчок позволяет сбросить масштаб. Используйте кнопки +/- для увеличения/уменьшения масштаба."], "Chart Navigation Features": ["Возможности навигации по графику"], "This chart supports interactive navigation:": ["Этот график поддерживает интерактивную навигацию:"], "Pan the chart:": ["Панорамируйте график:"], "Hold": ["Дер<PERSON>ите"], "while dragging with the mouse": ["при перетаскивании с помощью мыши"], "Zoom in/out:": ["Увеличение/уменьшение масштаба:"], "while scrolling the mouse wheel, or use": ["прокручивая колесико мыши, или используйте"], "and": ["и"], "keys": ["клавиши"], "Reset view:": ["Сбросить вид:"], "Press": ["Зажать"], "or click the Reset button": ["или нажмите кнопку Сброс"], "Got it!": ["Понятно!"], "This JavaScript code will be executed after the template is rendered.": ["Этот код JavaScript будет выполнен после отрисовки шаблона."], "You can use it to add event handlers, manipulate DOM elements inside container, etc.": ["Можно использовать, чтобы добавлять обработчики событий, манипулировать элементами DOM внутри контейнера графика и т.д."], "JavaScript Code": ["JavaScript код"], "Define JavaScript code to execute after the template is rendered": ["Укажите код <PERSON>Script, который будет выполняться после отрисовки шаблона"], "Allow navigation tools": ["Разрешить инструменты навигации"], "Enable navigation tools for the handlebars template. Allows panning with middle mouse button or Cmd/Alt+drag, and zooming with Cmd/Alt+mouse wheel.": ["Включить инструменты навигации для handlebars. Позволяет панорамировать с помощью левой кнопкой мыши или Cmd/Alt+drag, и масштабирование с помощью Cmd/Alt+колесико "], "Loading...": ["Загрузка..."], "Write a handlebars template to render the data": ["Напишите шаблон руля, чтобы отображать данные"], "Handlebars": ["Handlebars"], "must have a value": ["значение обязательно"], "Handlebars Template": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "A handlebars template that is applied to the data": ["Шаблон handlebars, примененный к данным"], "Include time": ["Включить время"], "Whether to include the time granularity as defined in the time section": ["Добавляет столбец даты/времени с группировкой дат, как определено в разделе Время"], "Percentage metrics": ["Процентные меры"], "Select one or many metrics to display, that will be displayed in the percentages of total. Percentage metrics will be calculated only from data within the row limit. You can use an aggregation function on a column or write custom SQL to create a percentage metric.": ["Выберите один или много показателей для отображения, которые будут отображаться в процентах от общего числа."], "Show summary": ["Показать итоги"], "Show total aggregations of selected metrics. Note that row limit does not apply to the result.": ["Показывает общие итоговые значения выбранных показателей. Обратите внимание, что ограничение количества строк не применяется к результату."], "Ordering": ["Упорядочивание"], "Order results by selected columns": ["Упорядочить результаты по выбранным столбцам"], "Sort descending": ["Сортировка по убыванию"], "Server pagination": ["Серверная пагинация"], "Enable server side pagination of results (experimental feature)": ["Включить серверную пагинацию результатов (экспериментально)"], "Server Page Length": ["Серверный размер страницы"], "Rows per page, 0 means no pagination": ["Строчек на странице, 0 означает все строки"], "Query mode": ["Режим запроса"], "Group By, Metrics or Percentage Metrics must have a value": ["Измерения, Меры или Процентные меры должны иметь значение"], "CSS Styles": ["CSS стили"], "CSS applied to the chart": ["CSS, примененный к графику"], "Columns to group by on the columns": ["Столбцы для группировки по столбцам"], "Rows": ["Строки"], "Columns to group by on the rows": ["Столбцы для группировки по строкам"], "Apply metrics on": ["Применить меры к"], "Use metrics as a top level group for columns or for rows": ["Используйте метрики в качестве группы верхнего уровня для столбцов или для рядов"], "Cell limit": ["<PERSON>имит ячеек"], "Limits the number of cells that get retrieved.": ["Ограничивает количество извлекаемых ячеек"], "Metric used to define how the top series are sorted if a series or cell limit is present. If undefined reverts to the first metric (where appropriate).": ["Мера, используемая для определения того, как сортируются верхние категории, если присутствует ограничение по категории или ячейке. Если не определено, возвращается к первой мере (где это уместно)."], "Aggregation function": ["Функция агрегирования"], "Aggregate function to apply when pivoting and computing the total rows and columns": ["Агрегатная функция, применяемая для сводных таблиц и вычислении суммарных значений."], "Show rows total": ["Показать общий итог по строкам"], "Display row level total": ["Отображать общий итог по строке"], "Show rows subtotal": ["Показать промежуточные итоги по строкам"], "Display row level subtotal": ["Показ уровня строки подтотальный"], "Show columns total": ["Показать общий итог по столбцам"], "Display column level total": ["Отображать общий итог по столбцу"], "Show columns subtotal": ["Показать промежуточные итоги по столбцам"], "Display column level subtotal": ["Попрос на уровне столбца подтотальный"], "Transpose pivot": ["Транспонировать таблицу"], "Swap rows and columns": ["Поменять местами строки и столбцы"], "Combine metrics": ["Объединить меры"], "Display metrics side by side within each column, as opposed to each column being displayed side by side for each metric.": ["Отображать меры рядом в каждом столбце, в отличие от отображения каждого столбца рядом для каждой меры."], "Adaptive formatting dot ddmmyyyy": ["%d.%m.%Y (гран.) | 14.01.2019"], "D3 time format for datetime columns": ["Формат времени D3 для столбцов типа дата/время"], "Sort rows by": ["Сортировка строк по"], "key a-z": ["По алфавиту А-Я"], "key z-a": ["По алфавиту Я-А"], "value ascending": ["Значение по возрастанию"], "value descending": ["Значение по убыванию"], "Change order of rows.": ["Сменить порядок строк."], "Available sorting modes:": ["Доступные режимы сортировки:"], "By key: use row names as sorting key": ["По ключу: использовать имена строк как ключ сортировки"], "By value: use metric values as sorting key": ["По значению: использовать значения мер как ключ сортировки"], "Sort columns by": ["Сортировать столбцы по"], "Change order of columns.": ["Сменить порядок столбцов."], "By key: use column names as sorting key": ["По ключу: использовать имена столбцов как ключ сортировки"], "Rows subtotal position": ["Расположение строк подытогов"], "Position of row level subtotal": ["Расположение промежуточного итога на уровне строки"], "Columns subtotal position": ["Расположение столбцов подытогов"], "Position of column level subtotal": ["Расположение промежуточного итога на уровне столбца"], "Customize columns": ["Настроить столбцы"], "Further customize how to display each column": ["Дальнейшая настройка отображения каждого столбца"], "Apply conditional color formatting to metrics": ["Применить условное цветовое форматирование к мерам"], "Used to summarize a set of data by grouping together multiple statistics along two axes. Examples: Sales numbers by region and month, tasks by status and assignee, active users by age and location. Not the most visually stunning visualization, but highly informative and versatile.": ["Используется для обобщения набора данных путем группировки нескольких статистических данных по двум осям. Примеры: Количество продаж по регионам и месяцам, задачи по статусу и назначенному лицу, активные пользователи по возрасту и местоположению. Не самая наглядная визуализация, но очень информативная и универсальная."], "Pivot Table": ["Сводная таблица"], "metric": ["мера"], "Subtotal": ["Подытог"], "Total (%(aggregatorName)s)": ["Итого (%(aggregatorName)s)"], "Value has own aggregation: %(aggregation)s": ["Значение имеет собственную агрегацию: %(aggregation)s"], "Unknown input format": ["Неизвестный формат ввода"], "Search %s records": ["Поиск %s записей"], "search.num_records": ["search.num_records"], "page_size.show": ["Показать"], "Show %s entries": ["Показывать %s записей"], "page_size.entries": ["запи<PERSON><PERSON>й"], "No matching records found": ["не найдено соответствующих записей"], "Display all": ["Отображать все"], "Main": ["Основной"], "Select columns that will be displayed in the table. You can multiselect columns.": ["Выберите столбцы, которые будут отображаться в таблице."], "Shift + Click to sort by multiple columns": ["Shift + Нажать для сортировки по нескольким столбцам"], "Summary": ["Итого"], "Display": ["Отображение"], "Number formatting": ["Форматирование чисел"], "Export": ["Экспортировать"], "Timestamp format": ["Формат даты и времени"], "Page length": ["Размер страницы"], "Search box": ["Строка поиска"], "Whether to include a client-side search box": ["Отображение строки поиска"], "Allow columns to be rearranged": ["Разрешить смену столбцов местами"], "Allow end user to drag-and-drop column headers to rearrange them. Note their changes won't persist for the next time they open the chart.": ["Разрешить конечному пользователю перемещать столбцы, удерживая их заголовки. Заметьте, такие изменения будут нейтрализованы при следующем обращении к дашборду."], "Render columns in HTML format": ["Отрисовывать столбцы в формате HTML"], "Render data in HTML format if applicable.": ["Отрисовывает данные в формате HTML, если применимо."], "Visual formatting": ["Визуальное форматирование"], "Show Cell bars": ["Показать столбцы ячеек"], "Whether to display a bar chart background in table columns": ["Отображать гистограмм в колонках таблицы"], "Align +/-": ["Выровнять +/-"], "Whether to align background charts with both positive and negative values at 0": ["Совместить ли фоновые диаграммы как с положительными, так и с отрицательными значениями при 0"], "add colors to cell bars for +/-": ["добавить цвета в столбцы для +/-"], "Whether to colorize numeric values by whether they are positive or negative": ["окрасить численные значения на то, являются ли они положительными или отрицательными"], "basic conditional formatting": ["основное условное форматирование"], "This will be applied to the whole table. Arrows (↑ and ↓) will be added to main columns for increase and decrease. Basic conditional formatting can be overwritten by conditional formatting below.": ["Это будет применено ко всей таблице."], "color type": ["тип цвета"], "Custom Conditional Formatting": ["Пользовательское условное форматирование"], "Apply conditional color formatting to numeric columns": ["Применить условное цветовое форматирование к столбцам"], "Classic row-by-column spreadsheet like view of a dataset. Use tables to showcase a view into the underlying data or to show aggregated metrics.": ["Классическое представление таблицы. Используйте таблицы для демонстрации отображения исходных или агрегированных данных."], "Show": ["Показать"], "entries": ["записи"], "Minimum Font Size": ["Минимальный размер шрифта"], "Font size for the smallest value in the list": ["Размер шрифта для наименьшего значения в списке"], "Maximum Font Size": ["Максимальный размер шрифта"], "Font size for the biggest value in the list": ["Размер шрифта для наибольшего значения в списке"], "Word Rotation": ["Поворот текста"], "random": ["случайно"], "square": ["квадрат"], "Rotation to apply to words in the cloud": ["Вращение для применения к словам в облаке"], "Visualizes the words in a column that appear the most often. Bigger font corresponds to higher frequency.": ["Визуализирует слова в столбце, которые появляются чаще всего. Более крупный шрифт соответствует более высокой частоте"], "Word Cloud": ["Облак<PERSON> слов"], "You cannot edit titles from Dataset": ["Вы не можете редактировать заголовки из Датасета"], "Editing filter set:": ["Редактирование набора фильтров:"], "Cancel": ["Отмена"], "Filter set with this name already exists": ["Набор фильтров с этим именем уже существует"], "Filter set already exists": ["Набор фильтров уже существует"], "Save": ["Сохранить"], "This filter set is identical to: \"%s\"": ["Этот набор фильтров идентичен \"%s\""], "Edit": ["Редактировать"], "The primary set of filters will be applied automatically": ["Основной набор фильтров будет применяться автоматически"], "Set as primary": ["Установить основным"], "Remove invalid filters": ["Удалить недействующие фильтры"], "Rebuild": ["Обновить"], "Delete": ["Удалить"], "Filters (%d)": ["Фильтры (%d)"], "This filter doesn't exist in dashboard. It will not be applied.": ["Этот фильтр не существует в дашборде. Он не будет применен."], "Filter metadata changed in dashboard. It will not be applied.": ["Метаданные фильтра изменились в дашборде. Они не будут применены."], "Please filter set name": ["Введите имя набора фильтров"], "Create": ["Создать"], "Create filter set": ["Создать набор фильтров"], "New filter set": ["Новый набор фильтров"], "Please apply filter changes": ["Пожалуйста, примените изменения фильтров"], "Unknown value": ["Неизвестная ошибка"], "Service temporarily unavailable": ["Сервис временно недоступен"], "Sorry, something went wrong. We are fixing the mistake now. Try again later.": ["Извините, что-то пошло не так. Сейчас мы исправляем ошибку. Повторите попытку позже."], "Edit label colors": ["Изменить цвета мер"], "Number of changes": ["Число изменений"], "Deleted": ["Удалено"], "Not assigned": ["Не назначено"], "Edit colors": ["Изменить цвета"], "The dashboard has a color scheme applied: ": ["На дашборде применена цветовая схема: "], "You can override the colors of the metrics with your own values.": ["Вы можете переопределить цвета метрик собственными значениями."], "Search for label": ["Искать меру"], "Filter labels": ["Фильтровать меры"], "On dashboard": ["На дашборде"], "Not on dashboard": ["Нет на дашборде"], "All": ["Все"], "Metric is missing from the dashboard with current filters or removed from the dataset": ["Мера отсутствует на дашборде с текущими фильтрами или удалена из датасета"], "Color": ["Цвет"], "Present on charts": ["Присутствует на графиках"], "No results match your filter criteria": ["Не найдено результатов по вашим критериям"], "Edit formatter": ["Редактировать форматер"], "Add new formatter": ["Добавить форматирование"], "Add new color formatter": ["Добавить цветовое форматирование"], "Color scheme": ["Цветовая схема"], "Apply": ["Применить"], "success": ["успех"], "alert": ["оповещение"], "error": ["ошибка"], "success dark": ["Успех темный"], "alert dark": ["бенжетная тьма"], "error dark": ["Ошибка темная"], "This value should be smaller than the right target value": ["Это значение должно быть больше чем правое целевое значение"], "This value should be greater than the left target value": ["Это значение должно быть больше чем левое целевое значение"], "Required": ["Обязательно"], "Operator": ["Оператор"], "Left value": ["Левое значение"], "Right value": ["Правое значение"], "Target value": ["Целевое значение"], "Select column": ["Выберите столбец"], "Message": ["Сообщение"], "Create new team": ["Создайте новую команду"], "Create team": ["Создать команду"], "User from": ["Пользователь из"], "slug": ["читаемый URL"], "New team will be created": ["Будет создана новая команда"], "User will update is on the next step": ["Пользователь будет обновлен на следующем шаге"], "Next": ["Следующий"], "Team Name": ["Название команды"], "Too short": ["Слишком короткий"], "Too long": ["Слишком длинное"], "Full Team Name": ["Полное название команды"], "enter team name": ["Введите название команды"], "You are welcome to Superset": ["Добро пожаловать в Суперсет"], "Tell us more about yourself": ["Расскажите нам о себе"], "All the data is from Dodo IS. Please enter your team or role. It helps to proceed your request.": ["Все данные взяты из Dodo IS. Пожалуйста, укажите свою команду или должность. Это поможет выполнить ваш запрос."], "First name": ["Имя"], "Last name": ["Фамилия"], "Role in Dodo Brands": ["Роль в Dodo Brands"], "Please input your role in Dodo Brands!": ["Пожалуйста, укажите вашу роль в Dodo Brands!"], "Minimum 3 characters": ["Минимум 3 символа"], "Maximum 30 characters": ["Максимум 30 символов"], "Next step": ["Следующий шаг"], "You request created": ["Ваша заявка создана"], "Request will be proceed by administrators. You can see your requests in": ["Заявка будет обработана администраторами. Вы можете увидеть ваши заявки в"], "profile": ["профиле"], "Tell us why you are here": ["Расскажите нам, почему вы здесь"], "Are you a franchisee or from a Managing Company?": ["Вы являетесь франчайзи или работаете в управляющей компании?"], "Create of find your team": ["Создайте или найдите вашу команду"], "Select `C_LEVEL` if you are a ‘C level’ in DODO": ["Выберите `C_LEVEL` если вы ‘C level’ в ДОДО"], "Your team name is": ["Название вашей команды:"], "Finish onboarding": ["Завершить онбординг"], "All C-level people please select ‘c_level’": ["Все люди C-уровня, пожалуйста, выберите «C_LEVEL»"], "your team": ["ваша команда"], "Which use cases are you interested in using Superset for?": ["Как вы планируете использовать Суперсет?"], "readonly": ["Анализ данных"], "Check available dashboards. Gather insights from charts inside a dashboard": ["Проверка доступных дашбордов. Получение информации из графиков внутри дашбордов"], "Create dashboards and charts": ["Создание дашбордов и графиков"], "Create dashboards. Create charts": ["Создание дашбордов, создание графиков"], "Create datasets and use SQL Lab": ["Создание датасетов и использование SQL Lab"], "Create datasets. Use SQL Lab for your Ad-hoc queries": ["Создание датасетов. Использование SQL Lab для Ad-hoc запросов"], "Based on your selection, your roles are:": ["Вы выбрали следующие роли"], "An error occurred while checking user's team": ["Произошла ошибка при получении команды пользователя."], "Team search": ["Поиск команды"], "Existing team": ["Существующая команда"], "Check information and update user": ["Проверьте информацию и обновите пользователя"], "If this is C-level": ["Если запрос от C-level"], "give: c_level": ["даем: c_level"], "If it's a franchisee": ["Если это франчайзи"], "Give: fr_{last name}_{first name}": ["Даем: fr_{фамилия}_{имя}"], "If from management company": ["Если это сотрудник УК"], "Give: by the name of the team": ["Даем: по названию команды"], "Team has been created successfully.": ["Команда была создана успешно."], "An error occurred while creating the team": ["Произошла ошибка при создании команды"], "Request closed successfully.": ["Запрос успешно закрыт."], "An error occurred while closing the request": ["Произошла ошибка при закрытии запроса"], "Onboarding request": ["Заявка на онбоардинг"], "Email": ["Электронная почта"], "Current roles": ["Текущие роли"], "Requested roles": ["Запрашиваемые роли"], "Closed": ["Закрыта"], "Request date": ["Дата заявки"], "Update date": ["Дата обновления"], "Check available dashboards": ["Проверка доступных дашбордов"], "Gather insights from charts inside a dashboard": ["Ана<PERSON><PERSON><PERSON> графиков в дашбордах"], "Create datasets": ["Создание датасетов"], "Use SQL Lab for your Ad-hoc queries": ["Использование SQL Lab для произвольных запросов"], "Create dashboards": ["Создание дашбордов"], "Create charts": ["Создание графиков"], "User update": ["Обновить пользователя"], "Apply roles and link to a team": ["Применить роли и добавить в команду"], "New Roles": ["Новые роли"], "Actions": ["Действия"], "Requests": ["Заявки"], "An error occurred while fetching statements user values: %s": ["Произошла ошибка при извлечении операторов операторов: %s"], "Any": ["Любой"], "Franchisee": ["Франчайзи"], "Members count": ["Количество участников"], "Add members": ["Добавить участников"], "select member": ["выберите участник"], "Members": ["Участники"], "Username": ["Имя пользователя"], "Created on": ["Дата создания"], "Login count": ["Количество логинов"], "Last login date": ["Дата последнего логина"], "REMOVE FROM TEAM": ["Удалить из команды"], "Member added successfully.": ["Участник успешно добавлен."], "Member removed successfully.": ["Участник успешно удален."], "An error occurred while adding user:": ["Произошла ошибка при добавлении пользователя:"], "An error occurred while removing user:": ["Произошла ошибка при удалении пользователя:"], "Teams": ["Команды"], "offline": ["офла<PERSON>н"], "failed": ["не удалось"], "pending": ["в ожидании"], "fetching": ["загружаем"], "running": ["обрабатываем"], "stopped": ["остановлен"], "The query couldn't be loaded": ["Запрос невозможно загрузить"], "Your query has been scheduled. To see details of your query, navigate to Saved queries": ["Запрос был запланирован. Чтобы посмотреть детали запроса, перейдите в Сохраненные запросы"], "Your query could not be scheduled": ["Не удалось запланировать ваш запрос"], "Failed at retrieving results": ["Невозможно выполнить запрос"], "Query was stopped.": ["Запрос прерван"], "Failed at stopping query. %s": ["Не удалось остановить запрос. %s"], "Unable to migrate table schema state to backend. Superset will retry later. Please contact your administrator if this problem persists.": ["Не удается перенести состояние схемы таблицы на сервер. Суперсет повторит попытку позже. Пожалуйста, свяжитесь с вашим администратором, если эта проблема не устранена."], "Unable to migrate query state to backend. Superset will retry later. Please contact your administrator if this problem persists.": ["Не удается перенести состояние запроса на сервер. Суперсет повторит попытку позже. Пожалуйста, свяжитесь с вашим администратором, если эта проблема не устранена."], "Unable to migrate query editor state to backend. Superset will retry later. Please contact your administrator if this problem persists.": ["Не удается перенести состояние редактора запроса на сервер. Суперсет повторит попытку позже. Пожалуйста, свяжитесь с вашим администратором, если эта проблема не устранена."], "-- Note: Unless you save your query, these tabs will NOT persist if you clear your cookies or change browsers.\n\n": ["— Примечание. Если вы не сохраните свой запрос, эти вкладки не будут сохраняться, если вы очистите свои файлы cookie или измените браузеры.\n\n"], "Copy of %s": ["Копия %s"], "An error occurred while fetching tab state": ["Произошла ошибка при получении данных вкладки"], "An error occurred while removing query. Please contact your administrator.": ["Произошла ошибка при удалении запроса. Пожалуйста, свяжитесь с администратором."], "Your query could not be saved": ["Не удалось сохранить ваш запрос"], "Your query was not properly saved": ["Ваш запрос не был сохранен должным образом"], "Your query was saved": ["Ваш запрос был сохранен"], "Your query was updated": ["Ваш запрос был сохранен"], "Your query could not be updated": ["Не удалось обновить ваш запрос"], "An error occurred while storing your query in the backend. To avoid losing your changes, please save your query using the \"Save Query\" button.": ["Произошла ошибка при сохранении запроса на сервере. Чтобы сохранить изменения, пожалуйста, сохраните ваш запрос через кнопку \"Сохранить как\"."], "An error occurred while fetching table metadata. Please contact your administrator.": ["Произошла ошибка при получении метаданных таблицы. Пожалуйста, свяжитесь с администратором."], "An error occurred while expanding the table schema. Please contact your administrator.": ["Произошла ошибка при разворачивании схемы. Пожалуйста, свяжитесь с администратором."], "An error occurred while collapsing the table schema. Please contact your administrator.": ["Произошла ошибка при сворачивании схемы. Пожалуйста, свяжитесь с администратором."], "An error occurred while removing the table schema. Please contact your administrator.": ["Произошла ошибка при удалении схемы. Пожалуйста, свяжитесь с администратором."], "Shared query": ["Общедоступный запрос"], "The datasource couldn't be loaded": ["Невозможно загрузить источник данных"], "An error occurred while creating the data source": ["Произошла ошибка при создании источника данных"], "An error occurred while fetching function names.": ["Произошла ошибка при получении имен функций"], "SQL Lab uses your browser's local storage to store queries and results.\nCurrently, you are using %(currentUsage)s KB out of %(maxStorage)d KB storage space.\nTo keep SQL Lab from crashing, please delete some query tabs.\nYou can re-access these queries by using the Save feature before you delete the tab.\nNote that you will need to close other SQL Lab windows before you do this.": ["SQL Lab использует локальное хранилище вашего браузера для хранения запросов и результатов. SQL Lab использует локальное хранилище вашего браузера для хранения запросов и результатов.\nВ настоящее время вы используете %(currentUsage)s КБ из %(maxStorage)d КБ дискового пространства.\n Чтобы предотвратить сбой Лаборатории SQL, пожалуйста, удалите некоторые вкладки запросов.\n Вы можете повторно получить доступ к этим запросам, используя функцию сохранения перед удалением вкладки.\n Обратите внимание, что перед этим вам нужно будет закрыть другие окна Лаборатории SQL."], "Primary key": ["Первичный ключ"], "Foreign key": ["Внеш<PERSON><PERSON> ключ"], "Index": ["Ин<PERSON><PERSON><PERSON>с"], "Estimate selected query cost": ["Оценить стоимость выбранного запроса"], "Estimate cost": ["Оценить стоимость запроса"], "Cost estimate": ["Прогноз затрат"], "Creating a data source and creating a new tab": ["Создание источника данных и добавление новой вкладки"], "Explore the result set in the data exploration view": ["Создать новый график на основе этих данных"], "explore": ["исследовать"], "Create Chart": ["Создать график"], "Source SQL": ["Исходный SQL"], "Executed SQL": ["Исполненный SQL"], "SQL": ["SQL"], "Run query": ["Выполнить запрос"], "Run current query": ["Запустите текущий запрос"], "Stop query": ["Остановить запрос"], "New tab": ["Новая вкладка"], "Previous Line": ["Предыдущая строка"], "Format SQL": ["Отформатировать SQL"], "Switch to the previous tab": ["Переключиться на предыдущую вкладку"], "Switch to the next tab": ["Переключиться на предыдущую вкладку"], "Find": ["Найти"], "Replace": ["Заменить"], "Keyboard shortcuts": ["Горячие клавиши"], "Run a query to display query history": ["Выполните запрос для отображения истории"], "LIMIT": ["ОГРАНИЧЕНИЕ"], "State": ["Состояние"], "Started": ["Начато"], "Duration": ["Продолжительность"], "Results": ["Результаты"], "Success": ["Успешно"], "Failed": ["Ошибка"], "Running": ["Выполняется"], "Fetching": ["Получение данных"], "Offline": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "Scheduled": ["Запланировано"], "Unknown Status": ["Неизвестный статус"], "View": ["Показать"], "Data preview": ["Предпросмотр данных"], "Overwrite text in the editor with a query on this table": ["Вставить этот запрос в редактор SQL"], "Run query in a new tab": ["Выполнить запрос на новой вкладке"], "Remove query from log": ["Удалить запрос из истории"], "Unable to create chart without a query id.": ["Невозможно создать график без идентификатора запроса."], "Save & Explore": ["Сохранить и исследовать"], "Overwrite & Explore": ["Перезаписать и исследовать"], "Save this query as a virtual dataset to continue exploring": ["Сохраните данный запрос как виртуальный датасет для создания графика"], "Download to CSV": ["Сохранить в CSV"], "Download to XLSX": ["Сохранить в XLSX"], "Copy to Clipboard": ["Скопировать в буфер обмена"], "Filter results": ["Фильтровать результаты"], "The number of results displayed is limited to %(rows)d by the configuration DISPLAY_MAX_ROW. Please add additional limits/filters or download to csv to see more rows up to the %(limit)d limit.": ["Количество отображаемых результатов ограничено %(rows)d с помощью конфигурации DISPLAY_MAX_ROW. Пожалуйста, добавьте дополнительные ограничения/фильтры или загрузите в csv, чтобы увидеть больше строк вплоть до %(limit)d."], "The number of results displayed is limited to %(rows)d. Please add additional limits/filters, download to csv, or contact an admin to see more rows up to the %(limit)d limit.": ["Количество отображаемых результатов ограничено %(rows)d. Пожалуйста, добавьте дополнительные ограничения/фильтры или загрузите в csv, чтобы увидеть больше строк до предела %(limit)d.\""], "The number of rows displayed is limited to %(rows)d by the query": ["Количество отображаемых строк ограничено: не более %(rows)d."], "The number of rows displayed is limited to %(rows)d by the limit dropdown.": ["Количество отображаемых строк ограничено: не более %(rows)d."], "The number of rows displayed is limited to %(rows)d by the query and limit dropdown.": ["Количество отображаемых строк ограничено: не более %(rows)d."], "%(rows)d rows returned": ["Получено строк: %(rows)d"], "The number of rows displayed is limited to %(rows)d by the dropdown.": ["Количество отображаемых строк ограничено %(rows)d на раскрывающемся списке."], "%s row": ["%s ряд"], "Track job": ["Отслеживать работу"], "See query details": ["Показать детали запроса"], "Query was stopped": ["Запрос прерван"], "Database error": ["Ошибка базы данных"], "Retry fetching results": ["Повторные результаты получения результатов"], "was created": ["создан(а)"], "Query in a new tab": ["Запрос в отдельной вкладке"], "The query returned no data": ["Запрос не вернул данных"], "Fetch data preview": ["Получить данные для просмотра"], "Refetch results": ["Выполнить запрос повторно"], "Stop": ["Стоп"], "Run selection": ["Выполнить выбранное"], "Run": ["Выполнить"], "Stop running (Ctrl + x)": ["Остановить выполнение (CTRL + x)"], "Stop running (Ctrl + e)": ["Остановить выполнение (CTRL + e)"], "Run query (Ctrl + Return)": ["Выполнить запрос (Ctrl + Enter)"], "Untitled Dataset": ["Безымянный датасет"], "An error occurred saving dataset": ["Произошла ошибка при сохранении датасета"], "Save or Overwrite Dataset": ["Сохранить или перезаписать датасет"], "Back": ["Назад"], "Save as new": ["Сохранить как новый"], "Overwrite existing": ["Перезаписать существующий"], "Select or type dataset name": ["Выберите/введите имя датасета"], "Existing dataset": ["Существующий датасет"], "Are you sure you want to overwrite this dataset?": ["Вы уверены, что хотите перезаписать этот датасет?"], "Undefined": ["Не определено"], "Save dataset": ["Сохранить датасет"], "Save as": ["Сохранить как"], "Save query": ["Сохранить запрос"], "Update": ["Обновить"], "Label for your query": ["Метка для вашего запроса"], "Write a description for your query": ["Заполните описание к вашему запросу"], "Submit": ["Отправить"], "Schedule query": ["Сохранить запрос"], "Schedule": ["Расписание"], "There was an error with your request": ["Произошла ошибка с вашим запросом"], "Please save the query to enable sharing": ["Пожалуйста, сохраните запрос, чтобы включить функцию \"поделиться\""], "Copy query link to your clipboard": ["Скопировать ссылку на запрос в буфер обмена"], "Save the query to enable this feature": ["Сохраните запрос для включения этой опции"], "Copy link": ["Скопировать ссылку"], "Run a query to display results": ["Выполните запрос для отображения результатов"], "No stored results found, you need to re-run your query": ["Не найдены сохраненные результаты, необходимо повторно выполнить запрос"], "Query history": ["История запросов"], "Preview: `%s`": ["Предпросмотр «%s»"], "Schedule the query periodically": ["Запланировать периодическое выполнение запроса"], "You must run the query successfully first": ["Сначала необходимо успешно выполнить запрос"], "Render HTML": ["Отрисовать HTML"], "Autocomplete": ["Автозаполнение"], "CREATE TABLE AS": ["CREATE TABLE AS"], "CREATE VIEW AS": ["CREATE VIEW AS"], "The database that was used to generate this query could not be found": ["база данных, которая использовалась для генерации этого запроса, не может быть найдена"], "Choose one of the available databases on the left panel.": ["Выберите одну из доступных баз данных из панели слева."], "Estimate the cost before running a query": ["Спрогнозировать стоимость до выполнения запроса"], "Specify name to CREATE VIEW AS schema in: public": ["Укажите имя нового представления для CREATE VIEW AS"], "Specify name to CREATE TABLE AS schema in: public": ["Укажите имя новой таблицы для CREATE TABLE AS"], "Select a database to write a query": ["Выберите базу данных для написания запроса"], "Choose one of the available databases from the panel on the left.": ["Выберите одну из доступных баз данных из панели слева."], "Collapse table preview": ["Свернуть предпросмотр таблицы"], "Expand table preview": ["Расширить предпросмотр таблицы"], "Reset state": ["Сбросить текущее состояние"], "Enter a new title for the tab": ["Введите новое название для вкладки"], "Close tab": ["Закрыть вкладку"], "Rename tab": ["Переименовать вкладку"], "Expand tool bar": ["Показать панель инструментов"], "Hide tool bar": ["Скрыть панель инструментов"], "Close all other tabs": ["Закрыть остальные вкладки"], "Duplicate tab": ["Дублировать вкладку"], "Add a new tab": ["Новая вкладка"], "New tab (Ctrl + q)": ["Новая вкладка (CTRL + Q)"], "New tab (Ctrl + t)": ["Новая вкладка (CTRL + T)"], "Add a new tab to create SQL Query": ["Откройте новую вкладку для создания SQL запроса"], "An error occurred while fetching table metadata": ["Произошла ошибка при получении метаданных из таблицы"], "Copy partition query to clipboard": ["Скопировать часть запроса в буфер обмена"], "latest partition:": ["последний раздел:"], "Keys for table": ["Ключи для таблицы"], "View keys & indexes (%s)": ["Показать ключи и индексы (%s)"], "Original table column order": ["Расположение столбцов как в исходной таблице"], "Sort columns alphabetically": ["Отсортировать столбцы в алфавитном порядке"], "Copy SELECT statement to the clipboard": ["Скопировать выражение SELECT в буфер обмена"], "Show CREATE VIEW statement": ["Показать выражение CREATE VIEW"], "CREATE VIEW statement": ["Выражение CREATE VIEW"], "Remove table preview": ["Убрать предпросмотр таблицы"], "Assign a set of parameters as": ["Задайте набор параметров в формате"], "below (example:": ["ниже (пример:"], "), and they become available in your SQL (example:": ["), и они станут доступны в ваших SQL запросах (пример:"], "by using": [", используя"], "Jinja templating": ["Шаблонизацию Jinja"], "syntax.": ["Синта<PERSON><PERSON><PERSON><PERSON>."], "Edit template parameters": ["Редактировать параметры шаблонизации Jinja"], "Parameters ": ["Параметры "], "Invalid JSON": ["Недопустимый формат JSON"], "Untitled query": ["Безымянный запрос"], "%s%s": ["%s%s"], "Control": ["Элемент"], "Before": ["До"], "After": ["После"], "Click to see difference": ["Нажмите для просмотра изменений"], "Altered": ["Измененено"], "Chart changes": ["Изменения графика"], "Modified by: %s": ["Изменено: %s"], "Loaded data cached": ["Данные загружены в кэш"], "Loaded from cache": ["Загружено из кэша"], "Click to force-refresh": ["Нажмите для принудительного обновления"], "Cached": ["Добавлено в кэш"], "Waiting on %s": ["Ожидаем %s"], "Waiting on database...": ["Ожидаем базу данных..."], "Add required control values to preview chart": ["Добавьте обязательные значения для предпросмотра графика"], "Your chart is ready to go!": ["Ваш график готов!"], "Click on \"Create chart\" button in the control panel on the left to preview a visualization or": ["Нажмите на кнопку \"Создать график\" на панели управления слева для просмотра графика или"], "click here": ["нажмите сюда"], "No results were returned for this query": ["Не было получено данных по этому запросу"], "Make sure that the controls are configured properly and the datasource contains data for the selected time range": ["Убедитесь, что настройки графика верно сконфигурированы и источник данных содержит данные для выбранного временного интервала"], "An error occurred while loading the SQL": ["Произошла ошибка при загрузке SQL"], "Sorry, an error occurred": ["Извините, произошла ошибка"], "Updating chart was stopped": ["Обновление графика остановлено"], "An error occurred while rendering the visualization: %s": ["Произошла ошибка при построении графика: %s"], "Network error.": ["Ошибка сети."], "Cross-filter will be applied to all of the charts that use this dataset.": ["Cross Filter будет применен ко всем графикам, которые используют этот датасет."], "You can also just click on the chart to apply cross-filter.": ["Вы также можете просто нажать на график, чтобы применить кроссфильтр."], "Cross-filtering is not enabled for this dashboard.": ["Кроссфильтрация не включена для этого дашборда."], "This visualization type does not support cross-filtering.": ["Этот тип визуализации не поддерживает перекрестную фильтрацию."], "You can't apply cross-filter on this data point.": ["Вы не можете применить кроссфильтр в этой точке данных."], "Remove cross-filter": ["Снимите кроссфильтр"], "Add cross-filter": ["Добавить кроссфильтр"], "Failed to load dimensions for drill by": ["не удалось загрузить размеры для тренировки"], "Drill by is not yet supported for this chart type": ["Drill By еще не поддерживается для этого типа графика"], "Drill by is not available for this data point": ["Drill By не доступна для этой точки данных"], "Drill by": ["Drill by"], "Search columns": ["Поисковые столбцы"], "No columns found": ["Колонны не найдены"], "Failed to generate chart edit URL": ["Не удалось сгенерировать URL для редактирования графика"], "You do not have sufficient permissions to edit the chart": ["У вас нет достаточных разрешений для редактирования графика"], "Edit chart": ["Редактировать график"], "Close": ["Закрыть"], "Failed to load chart data.": ["Не удалось загрузить данные графика."], "Drill by: %s": ["Drill by: %s"], "There was an error loading the chart data": ["Ошибка загрузки данных графика"], "Results %s": ["Результаты %s"], "Drill to detail": ["Drill to detail"], "Drill to detail by": ["Drill to detail by"], "Drill to detail is disabled for this database. Change the database settings to enable it.": ["Drill to detail отключен для этой базы данных."], "Drill to detail is disabled because this chart does not group data by dimension value.": ["Drill to detail отключен, потому что этот график не группирует данные по значению измерения."], "Right-click on a dimension value to drill to detail by that value.": ["Щелкните правой кнопкой мыши по значению измерения, чтобы тренировать, чтобы детализировать это значение."], "Drill to detail by value is not yet supported for this chart type.": ["Drill to detail по значению еще не поддерживается для этого типа графика."], "Drill to detail: %s": ["Drill to detail: %s"], "Formatting": ["Форматирование"], "Formatted value": ["Форматированное значение"], "No rows were returned for this dataset": ["Не было получено данных для этого датасета"], "Reload": ["Обновить"], "Copy": ["Копировать"], "Copy to clipboard": ["Скопировать в буфер обмена"], "Copied to clipboard!": ["Скопировано в буфер обмена!"], "Sorry, your browser does not support copying. Use Ctrl / Cmd + C!": ["Извините, Ваш браузер не поддерживание копирование. Используйте сочетание клавиш [CTRL + C] для WIN или [CMD + C] для MAC!"], "every": ["каждые"], "every month": ["каждый месяц"], "every day of the month": ["каждый день месяца"], "day of the month": ["день месяца"], "every day of the week": ["каждый день недели"], "day of the week": ["день недели"], "every hour": ["каждый час"], "every minute": ["каждая минута"], "minute": ["минута"], "reboot": ["обновить"], "Every": ["Каждый(ая)"], "in": ["в"], "on": ["По"], "or": ["или"], "at": ["в"], ":": [":"], "minute(s)": ["минут"], "Invalid cron expression": ["Недопустимое CRON выражение"], "Clear": ["Очистить"], "Sunday": ["Воскресенье"], "Monday": ["Понедельник"], "Tuesday": ["Вторник"], "Wednesday": ["Среда"], "Thursday": ["Четверг"], "Friday": ["Пятница"], "Saturday": ["Суббота"], "January": ["Январь"], "February": ["Февраль"], "March": ["Ма<PERSON><PERSON>"], "April": ["Апрель"], "May": ["<PERSON><PERSON><PERSON>"], "June": ["Июнь"], "July": ["Июль"], "August": ["Август"], "September": ["Сентябрь"], "October": ["Октябрь"], "November": ["Ноябрь"], "December": ["Декабрь"], "SUN": ["ВС"], "MON": ["ПН"], "TUE": ["ВТ"], "WED": ["СР"], "THU": ["ЧТ"], "FRI": ["ПТ"], "SAT": ["СБ"], "JAN": ["ЯНВ"], "FEB": ["ФЕВ"], "MAR": ["МАР"], "APR": ["АПР"], "MAY": ["МАЙ"], "JUN": ["ИЮН"], "JUL": ["ИЮЛ"], "AUG": ["АВГ"], "SEP": ["СЕН"], "OCT": ["ОКТ"], "NOV": ["НОЯ"], "DEC": ["ДЕК"], "There was an error loading the schemas": ["Возникла ошибка при загрузке схем"], "There was an error loading the catalogs": ["Произошла ошибка, загружающая каталоги"], "Select database or type to search databases": ["Выберите базу данных и введите текст для поиска"], "Force refresh catalog list": ["Принудительно обновить список каталогов"], "Select catalog or type to search catalogs": ["Выберите значение"], "Catalog": ["Каталог"], "No compatible catalog found": ["Совместимый каталог не найден"], "Force refresh schema list": ["Принудительно обновить список схем"], "Select schema or type to search schemas": ["Выберите каталог или введите для поиска"], "No compatible schema found": ["Не найдена совместимая схема"], "Warning! Changing the dataset may break the chart if the metadata does not exist.": ["Внимание! Изменение датасета может привести к тому, что график станет нерабочим, если будут отсутствовать метаданные."], "Changing the dataset may break the chart if the chart relies on columns or metadata that does not exist in the target dataset": ["Изменение датасета может привести к тому, что график станет нерабочим, если график использует несуществующие в целевом датасете столбцы или метаданные"], "dataset": ["да<PERSON><PERSON><PERSON><PERSON>т"], "Successfully changed dataset!": ["Успешное изменение датасета!"], "Connection": ["База данных"], "Swap dataset": ["Сменить датасет"], "Proceed": ["Продолжить"], "Warning!": ["Предупреждение!"], "Search / Filter": ["Поиск / Фильтр"], "Add item": ["Добавить запись"], "STRING": ["Строчный (STRING/VARCHAR)"], "NUMERIC": ["Числовой (NUMERIC/DECIMAL)"], "DATETIME": ["Дата/Время (DATETIME/TIMESTAMP)"], "BOOLEAN": ["Булевый (BOOLEAN)"], "Physical (table or view)": ["Физический (таблица или представление)"], "Virtual (SQL)": ["Виртуальный (SQL)"], "Label (Eng)": ["Английское название"], "Label (Rus)": ["Русское название"], "Data type": ["Тип данных"], "Advanced data type": ["Расширенный тип данных"], "Advanced Data type": ["Расширенный тип данных"], "Datetime format": ["Формат даты/времени"], "The pattern of timestamp format. For strings use ": ["Шаблон формата отметки времени (таймштампа). Для строк используйте "], "Python datetime string pattern": ["Шаб<PERSON>он строки даты и времени Python"], " expression which needs to adhere to the ": [", который должен придерживаться "], "ISO 8601": ["ISO 8601"], " standard to ensure that the lexicographical ordering\n                      coincides with the chronological ordering. If the\n                      timestamp format does not adhere to the ISO 8601 standard\n                      you will need to define an expression and type for\n                      transforming the string into a date or timestamp. Note\n                      currently time zones are not supported. If time is stored\n                      in epoch format, put `epoch_s` or `epoch_ms`. If no pattern\n                      is specified we fall back to using the optional defaults on a per\n                      database/column name level via the extra parameter.": ["Стандарт, чтобы гарантировать, что лексикографическое упорядочение совпадает с хронологическим упорядочением.  стандарта для обеспечения того, чтобы лексикографический порядок совпадал с хронологическим порядком. Если формат временной метки не соответствует стандарту ISO 8601, вам нужно будет определить выражение и тип для преобразования строки в дату или временную метку. В настоящее время часовые пояса не поддерживаются. Если время хранится в формате эпохи, введите \\`epoch_s\\` или \\`epoch_ms\\`. Если шаблон не указан, будут использованы необязательные значения по умолчанию на уровне имен для каждой базы данных/столбца с помощью дополнительного параметра."], "Certified By": ["Кем утверждено"], "Person or group that has certified this metric": ["Лицо или группа, которые утвердили этот показатель"], "Certified by": ["Кем утверждено"], "Certification details": ["Детали утверждения"], "Details of the certification": ["Детали утверждения"], "Is dimension": ["Является измерением"], "Default datetime": ["Дата и время по умолчанию"], "Is filterable": ["Фильтруемый"], "<new column>": ["<новый столбец>"], "Select owners": ["Выбрать владельцев"], "Modified columns: %s": ["Изменённые столбцы: %s"], "Removed columns: %s": ["Удалённые столбцы: %s"], "New columns added: %s": ["Добавленные столбцы: %s"], "Metadata has been synced": ["Метаданные синхронизированы"], "An error has occurred": ["Произошла ошибка"], "Column name [%s] is duplicated": ["Имя столбца [%s] является дубликатом"], "Metric name [%s] is duplicated": ["Дубль имени меры [%s]"], "Calculated column [%s] requires an expression": ["Для вычисляемого столбца [%s] требуется выражение"], "Invalid currency code in saved metrics": ["Неверный код валюты в сохраненных метриках"], "Basic": ["Базовая настройка"], "Default URL": ["URL по умолчанию"], "Default URL to redirect to when accessing from the dataset list page": ["URL по умолчанию, на который будет выполнен редирект при доступе из страницы со списком датасетов"], "Autocomplete filters": ["Фильтры автозаполнения"], "Whether to populate autocomplete filters options": ["Распространить настройки фильтров автозаполнения"], "Autocomplete query predicate": ["Предикат запроса автозаполнения"], "When using \"Autocomplete filters\", this can be used to improve performance of the query fetching the values. Use this option to apply a predicate (WHERE clause) to the query selecting the distinct values from the table. Typically the intent would be to limit the scan by applying a relative time filter on a partitioned or indexed time-related field.": ["Использование \"Autocomplete Filters\" может повысить производительность запроса, получая значения. Используйте эту опцию для настройки предиката (оператор WHERE) запроса для уникальных значений из таблицы. Обычно целью является ограничение сканирования путем применения относительного временного фильтра к секционированному или индексированному полю типа дата/время."], "Extra data to specify table metadata. Currently supports metadata of the format: `{ \"certification\": { \"certified_by\": \"Data Platform Team\", \"details\": \"This table is the source of truth.\" }, \"warning_markdown\": \"This is a warning.\" }`.": ["Дополнительные данные для указания метаданных таблиц. Дополнительные метаданные таблицы. В настоящий момент поддерживается следующий формат: `{ \"certification\": { \"certified_by\": \"Руководитель отдела\", \"details\": \"Эта таблица - источник правды.\" }, \"warning_markdown\": \"Это предупреждение.\" }`."], "Cache timeout": ["Время жизни кэша"], "The duration of time in seconds before the cache is invalidated. Set to -1 to bypass the cache.": ["Продолжительность времени за несколько секунд до того, как кэш недействителен. Установите -1 для обхода кэша."], "Hours offset": ["Смещение времени"], "The number of hours, negative or positive, to shift the time column. This can be used to move UTC time to local time.": ["Количество часов, отрицательное или положительное, для сдвига столбца формата дата/время. Это может быть использовано для приведения часового пояса UTC к местному времени."], "Normalize column names": ["Нормализуйте имена столбцов"], "Always filter main datetime column": ["Всегда фильтровать основной столбец времени даты"], "When the secondary temporal columns are filtered, apply the same filter to the main datetime column.": ["Когда вторичные временные столбцы отфильтрованы, применить тот же фильтр к основному столбцу времени даты."], "<new spatial>": ["<новая пространственная мера>"], "<no type>": ["<без типа>"], "Click the lock to make changes.": ["Нажмите на замок для внесения изменений."], "Click the lock to prevent further changes.": ["Нажмите на замок для запрета на внос изменений."], "virtual": ["Виртуальный"], "Dataset name": ["Имя датасета"], "When specifying SQL, the datasource acts as a view. Superset will use this statement as a subquery while grouping and filtering on the generated parent queries.": ["Когда указан SQL, источник данных работает как представление. Superset будет использовать это выражение в подзапросе, при необходимости группировки и фильтрации."], "Physical": ["Физический"], "The pointer to a physical table (or view). Keep in mind that the chart is associated to this Superset logical table, and this logical table points the physical table referenced here.": ["Указатель на физическую таблицу (или представление). Следует помнить, что график связан с логической таблицей Superset, а эта логическая таблица указывает на физическую таблицу, указанную здесь."], "This field is used as a unique identifier to attach the metric to charts. It is also used as the alias in the SQL query.": ["Это поле используется в качестве уникального идентификатора для прикрепления метрики к графикам. Оно также используется в качестве алиаса в SQL-запросе."], "D3 format": ["Формат даты/времени"], "Metric currency": ["Валюта меры"], "Select or type currency symbol": ["Выберите или введите символ валюты"], "Warning": ["Предупреждение"], "Optional warning about use of this metric": ["Необязательное предупреждение об использовании этой меры"], "<new metric>": ["<новая мера>"], "Be careful.": ["Будьте осторожны."], "Changing these settings will affect all charts using this dataset, including charts owned by other people.": ["Изменение этих настроек будет влиять на все графики, использующие этот датасет, включая графики других пользователей."], "Sync columns from source": ["Синхронизировать столбцы из источника"], "Calculated columns": ["Вычисляемые столбцы"], "This field is used as a unique identifier to attach the calculated dimension to charts. It is also used as the alias in the SQL query.": ["Это поле используется в качестве уникального идентификатора для прикрепления рассчитанного измерения к графикам."], "<enter SQL expression here>": ["<введите SQL выражение>"], "Settings": ["Настройки"], "The dataset has been saved": ["Дата<PERSON><PERSON>т сохранен"], "Error saving dataset": ["Ошибка при сохранении датасета"], "The dataset configuration exposed here\n                affects all the charts using this dataset.\n                Be mindful that changing settings\n                here may affect other charts\n                in undesirable ways.": ["Конфигурация Датасета, представленная здесь,\n                влияет на все графики, использующие этот датасет.\n                Помните, что изменение настроек\n                может иметь нежелаемый эффект\n                на другие графики."], "Are you sure you want to save and apply changes?": ["Вы уверены, что хотите сохранить и применить изменения?"], "Confirm save": ["Подтвердить сохранение"], "OK": ["ОК"], "Edit Dataset ": ["Редактировать датасет "], "Use legacy datasource editor": ["Использовать старый редактор"], "This dataset is managed externally, and can't be edited in Superset": ["Этот датасет управляется извне и не может быть изменена в Суперсете"], "DELETE": ["УДАЛИТЬ"], "Type \"%s\" to confirm": ["Введите \"%s\" для подтверждения"], "More": ["Подробнее"], "Click to edit": ["Нажмите для редактирования"], "You don't have the rights to alter this title.": ["Недостаточно прав для изменения названия."], "No databases match your search": ["Нет баз данных, удовлетворяющих вашему поиску"], "There are no databases available": ["Нет доступных баз данных"], "Manage your databases": ["Управляйте своими базами данных"], "here": ["здесь"], "Unexpected error": ["Неожиданная ошибка"], "This may be triggered by:": ["Возможные причины:"], "Please reach out to the Chart Owner for assistance.": ["Пожалуйста, обратитесь к владельцу графика."], "Chart Owner: %s": ["Владелец графика: %s"], "%(message)s\nThis may be triggered by: \n%(issues)s": ["%(message)s\nЭто может быть вызвано:\n%(issues)s"], "%s Error": ["%s Ошибка"], "Missing dataset": ["Отсутствующий датасет"], "See more": ["Подробнее"], "See less": ["Скрыть подробности"], "Copy message": ["Скопировать сообщение"], "Details": ["Детали"], "Authorization needed": ["Требуется разрешение"], "This was triggered by:": ["Это было вызвано:"], "Did you mean:": ["Возможно вы имели в виду:"], "%(suggestion)s instead of \"%(undefinedParameter)s?\"": ["%(suggestion)s вместо \\ “%(undefinedParameter)s?”"], "Parameter error": ["Ошибка параметра"], "We’re having trouble loading this visualization. Queries are set to timeout after %s second.": ["У нас проблемы с загрузкой этой визуализации. Запросы выполняются по тайм-ауту через %s секунд."], "We’re having trouble loading these results. Queries are set to timeout after %s second.": ["У нас проблемы с загрузкой этих результатов. Запросы выполняются по тайм-ауту через %s секунд."], "%(subtitle)s\nThis may be triggered by:\n %(issue)s": ["%(subtitle)s\nЭто может быть вызвано:\n%(issue)s"], "Timeout error": ["Ошибка таймаута"], "Click to favorite/unfavorite": ["Добавить в избранное"], "Cell content": ["Содержимое ячейки"], "Hide password.": ["Скрыть пароль."], "Show password.": ["Показать пароль."], "Database driver for importing maybe not installed. Visit the Superset documentation page for installation instructions: ": ["Драйвер базы данных для импорта может быть не установлен. Изучите документацию Суперсета для инструкций по установке: "], "OVERWRITE": ["ПЕРЕЗАПИСАТЬ"], "Database passwords": ["Пароли базы данных"], "%s PASSWORD": ["%s ПАРОЛЬ"], "%s SSH TUNNEL PASSWORD": ["%s Пароль SSH Tunnel"], "%s SSH TUNNEL PRIVATE KEY": ["%s Приватный ключ SSH Tunnel"], "%s SSH TUNNEL PRIVATE KEY PASSWORD": ["%s ПАРОЛЬ ПРИВАТНОГО КЛЮЧА SSH TUNNEL"], "Overwrite": ["Перезаписать"], "Import": ["Импорт"], "Import %s": ["Импортировать %s"], "Select file": ["Выбрать файл"], "Last Updated %s": ["Дата изменения %s"], "Sort": ["Сортировка"], "+ %s more": ["+ еще %s"], "%s Selected": ["%s Выбрано"], "Deselect all": ["Снять выделение"], "Add Tag": ["Добавить тег"], "Try different criteria to display results.": ["Попробуйте использовать другии критерии фильтрации"], "clear all filters": ["Сбросить все фильтры"], "No Data": ["Нет данных"], "%s-%s of %s": ["%s-%s из %s"], "Start date": ["Начало"], "End date": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Type a value": ["Введите значение"], "Filter": ["Фильтр"], "Select or type a value": ["Выберите значение"], "Last modified": ["Последнее изменение"], "Modified by": ["Кем изменено"], "Created by": ["Кем создано"], "Tags": ["Теги"], "Menu actions trigger": ["Действия меню триггер"], "Select ...": ["Выбрать ..."], "Filter menu": ["Меню фильтра"], "No filters": ["Нет филь<PERSON>ров"], "Select all items": ["Выбрать все записи"], "Search in filters": ["Поиск в фильтрах"], "Select current page": ["Выбрать текущую страницу"], "Invert current page": ["Инвертировать текущую страницу"], "Clear all data": ["Очистить все данные"], "Select all data": ["Выбрать все данные"], "Expand row": ["Развернуть строку"], "Collapse row": ["Свернуть строку"], "Click to sort descending": ["Нажмите для сортировки по убыванию"], "Click to sort ascending": ["Нажмите для сортировки по возрастанию"], "Click to cancel sorting": ["Нажмите для отмены сортировки"], "List updated": ["Список обновлен"], "There was an error loading the tables": ["Возникла ошибка при загрузке таблиц"], "See table schema": ["Таблица"], "Select table or type to search tables": ["Выберите таблицу или введите текст для поиска таблиц"], "Force refresh table list": ["Принудительно обновить список таблиц"], "You do not have permission to read tags": ["У вас нет разрешения на чтение тегов"], "Timezone selector": ["Выбор часового пояса"], "Failed to save cross-filter scoping": ["Не удалось сохранить кросс-фильтровую привязку"], "Dashboard label colors updated": ["Обновлены цвета меток дашборда"], "Failed to save dashboard label colors": ["Не удалось сохранить цвета меток дашборда"], "There is not enough space for this component. Try decreasing its width, or increasing the destination width.": ["Недостаточно пространства для этого компонента. Попробуйте уменьшить ширину или увеличить целевую ширину."], "Can not move top level tab into nested tabs": ["Невозможно перенести вкладку верхнего уровня во вложенную вкладку"], "This chart has been moved to a different filter scope.": ["Этот график был перемещён в другой набор фильтров."], "There was an issue fetching the favorite status of this dashboard.": ["Произошла ошибка с получением статуса избранного для этого дашборда."], "There was an issue favoriting this dashboard.": ["Произошла ошибка при добавлении этого дашборда в избранное."], "This dashboard is now published": ["Дашборд теперь опубликован"], "This dashboard is now hidden": ["Дашборд теперь скрыт"], "You do not have permissions to edit this dashboard.": ["У вас нет прав на редактирование этого дашборда."], "This dashboard was saved successfully.": ["Дашборд успешно сохранен."], "Sorry, an unknown error occurred": ["Извините, произошла неизвестная ошибка"], "Sorry, there was an error saving this dashboard: %s": ["Извините, произошла ошибка при сохранении дашборда: %s"], "You do not have permission to edit this dashboard": ["У вас нет прав на редактирование этого дашборда"], "Please confirm the overwrite values.": ["Пожалуйста, подтвердите перезаписные значения."], "You have used all %(historyLength)s undo slots and will not be able to fully undo subsequent actions. You may save your current state to reset the history.": ["Вы использовали все слоты %(historyLength)s  для отмены действий и не сможете полностью отменить последующие действия."], "Could not fetch all saved charts": ["Не удалось получить все сохраненные графики"], "Sorry there was an error fetching saved charts: ": ["Извините, произошла ошибка при загрузке графиков: "], "Any color palette selected here will override the colors applied to this dashboard's individual charts": ["Любая палитра, выбранная здесь, будет перезаписывать цвета, применённые к отдельным графикам этого дашборда"], "You have unsaved changes.": ["У вас есть несохраненные изменения."], "Drag and drop components and charts to the dashboard": ["Переместите элементы оформления и графики на дашборд"], "You can create a new chart or use existing ones from the panel on the right": ["Вы можете создать новый график или использовать существующие из панели справа"], "Create a new chart": ["Создать новый график"], "Drag and drop components to this tab": ["Переместите элементы оформления и графики в эту вкладку"], "There are no components added to this tab": ["В этой вкладке нет компонентов"], "You can add the components in the edit mode.": ["Вы можете добавить компоненты в режиме редактирования."], "Edit the dashboard": ["Изменить"], "There is no chart definition associated with this component, could it have been deleted?": ["С этим компонентом не связан ни один график, возможно, он был удален?"], "Delete this container and save to remove this message.": ["Удалите этот контейнер и сохраните изменения, чтобы убрать это сообщение."], "Refresh interval saved": ["Интервал обновления сохранен"], "Custom interval": ["Пользовательский интервал"], "Put positive values and valid minute and second value less than 60": ["Поместите положительные значения и действительное минутное и второе значение менее 60"], "Put some positive value greater than 0": ["Поместите некоторое положительное значение больше 0"], "Refresh interval": ["Интервал обновления"], "Refresh frequency": ["Частота обновления"], "HOUR": ["час"], "Type a number": ["Введите число"], "MINUTE": ["минута"], "Minutes value": ["Протокол"], "minutes": ["минут"], "SECOND": ["секунда"], "Seconds value": ["Секундные значения"], "seconds": ["секунд"], "Are you sure you want to proceed?": ["Вы уверены, что хотите продолжить?"], "Save for this session": ["Сохранить на время текущей сессии"], "You must pick a name for the new dashboard": ["Вы должны выбрать имя для нового дашборда"], "Save dashboard": ["Сохранить дашборд"], "Overwrite Dashboard [%s]": ["Перезаписать дашборд [%s]"], "Save as:": ["Сохранить как:"], "[dashboard name]": ["[имя дашборда]"], "also copy (duplicate) charts": ["также копировать (дублировать) графики"], "viz type": ["тип визуализации"], "recent": ["недавние"], "Create new chart": ["Создать новый график"], "Filter your charts": ["Поиск"], "Filter charts": ["Фильтрующие графики"], "Sort by %s": ["Сорт. по %s"], "Show only my charts": ["Показать только мои графики"], "You can choose to display all charts that you have access to or only the ones you own.\n              Your filter selection will be saved and remain active until you choose to change it.": ["Вы можете отобразить все графики, к которым у вас есть доступ, или только те, которые у вас есть."], "Added": ["Добавлено"], "Unknown type": ["Неизвестный тип"], "Viz type": ["Тип визуализации"], "Dataset": ["<PERSON>а<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Superset chart": ["График Superset"], "Check out this chart in dashboard:": ["Посмотреть этот график в дашборде:"], "Layout elements": ["Оформление"], "An error occurred while fetching available CSS templates": ["Произошла ошибка при получении доступных CSS-шаблонов"], "Load a CSS template": ["Загрузить CSS шаблон"], "Live CSS editor": ["Редактор CSS"], "Collapse tab content": ["Свернуть содержимое вкладки"], "There are no charts added to this dashboard": ["В этот дашборд еще не добавлен ни один график."], "Go to the edit mode to configure the dashboard and add charts": ["Перейдите в режим редактирования для изменения дашборда и добавьте графики"], "Unable to load dashboard": ["Невозможно загрузить панель панели"], "The following filters have the 'Select first filter value by default'\n                    option checked and could not be loaded, which is preventing the dashboard\n                    from rendering: %s": ["Следующие фильтры имеют значение «Выбрать первое значение фильтра по умолчанию» \\ n, и не может быть загружено, что предотвращает рендеринг на панель \\ n: %s"], "Processing file export...": ["Экспорт файла обрабатывается..."], "Changes saved.": ["Изменения сохранены."], "Disable embedding?": ["Выключить встраивание?"], "This will remove your current embed configuration.": ["Это удалит вашу текущую конфигурацию встраивания."], "Embedding deactivated.": ["Встраивание отключено"], "Sorry, something went wrong. Embedding could not be deactivated.": ["Извините, что-то пошло не так. Встраивание не может быть деактивировано."], "Sorry, something went wrong. Please try again.": ["Извините, что -то пошло не так."], "This dashboard is ready to embed. In your application, pass the following id to the SDK:": ["Этот дашборд готов к встраиванию. В своем приложении передайте SDK следующий идентификатор:"], "Configure this dashboard to embed it into an external web application.": ["Настройте этот дашборд для встраивания во внешнее веб-приложение"], "For further instructions, consult the": ["Для получения дальнейших инструкций обратитесь к"], "Superset Embedded SDK documentation.": ["Документация Superset встроена SDK."], "Allowed Domains (comma separated)": ["Разрешенные домены (разделить запятыми)"], "A list of domain names that can embed this dashboard. Leaving this field empty will allow embedding from any domain.": ["Список доменных имен, которые могут встраивать этот дашборд. Если оставить поле пустым, любой домен сможет сделать встраивание."], "Deactivate": ["Выключить"], "Save changes": ["Сохранить изменения"], "Enable embedding": ["Разрешить встраивание"], "Embed": ["Встроить"], "Applied filters (%s)": ["Применены фильтры (%s)"], "Applied cross-filters (%d)": ["Применены кросс-фильтры (%d)"], "Applied filters (%d)": ["Применены фильтры (%d)"], "This dashboard is currently auto refreshing; the next auto refresh will be in %s.": ["В настоящий момент дашборд обновляется; следующее обновление будет через %s."], "Your dashboard is too large. Please reduce its size before saving it.": ["Дашборд слишком большой. Пожалуйста, уменьшите его размер перед сохранением."], "Not available": ["Не доступно"], "Add the name of the dashboard": ["Задайте имя дашборда"], "Dashboard title": ["Название дашборда"], "Undo the action": ["Отменить действие"], "Redo the action": ["Повторить действие"], "Discard": ["Отменить изменения"], "Edit dashboard": ["Изменить"], "Refreshing charts": ["Обновление графиков"], "Superset dashboard": ["Дашборд Superset"], "Check out this dashboard: ": ["Посмотреть дашборд: "], "Refresh dashboard": ["Обновить дашборд"], "Exit fullscreen": ["Выйти из полноэкранного режима"], "Enter fullscreen": ["Полноэкранный режим"], "Edit properties": ["Редактировать свойства"], "Edit CSS": ["Редактировать CSS"], "Download": ["Скачать"], "Export to PDF": ["Экспорт в PDF"], "Download as Image": ["Скачать как изображение"], "Share": ["Поделиться"], "Copy permalink to clipboard": ["Скопировать ссылку в буфер обмена"], "Share permalink by email": ["Поделиться ссылкой по email"], "Embed dashboard": ["Встроить дашборд"], "Manage email report": ["Управление рассылкой по почте"], "Set filter mapping": ["Установить действие фильтра"], "Set auto-refresh interval": ["Задать интервал обновления"], "Confirm overwrite": ["Подтвердить перезапись"], "Scroll down to the bottom to enable overwriting changes. ": ["Прокрутите вниз внизу, чтобы обеспечить перезаписание изменений. "], "Yes, overwrite changes": ["Да, перезаписать изменения"], "Are you sure you intend to overwrite the following values?": ["Вы уверены, что хотите перезаписать эти значения?"], "Last Updated %s by %s": ["Изменено %s пользователем %s"], "Error": ["Ошибка"], "A valid color scheme is required": ["Требуется корректная цветовая схема"], "JSON metadata is invalid!": ["JSON метаданные не валидны!"], "Dashboard properties updated": ["Свойства дашборда обновлены"], "The dashboard has been saved": ["Да<PERSON>борд сохранен"], "Access": ["Доступ"], "Owners is a list of users who can alter the dashboard. Searchable by name or username.": ["Владельцы – это список пользователей, которые могут изменять дашборд. Можно искать по имени или никнейму."], "Colors": ["Цвета"], "Roles is a list which defines access to the dashboard. Granting a role access to a dashboard will bypass dataset level checks. If no roles are defined, regular access permissions apply.": ["Роли - это список, определяющий доступ к дашборду. Предоставление роли доступа к дашборду позволяет обойти проверки на уровне датасета. Если роли не определены, применяются обычные права доступа."], "Dashboard properties": ["Свойства дашборда"], "This dashboard is managed externally, and can't be edited in Superset": ["Эта панель управления управляется снаружи и не может быть отредактирована в Superset"], "Basic information": ["Основная информация"], "Title (Eng)": ["Английское название"], "Title (Rus)": ["Русское название"], "URL slug": ["Читаемый URL"], "A readable URL for your dashboard": ["Читаемый URL-адрес для дашборда"], "Certification": ["Утверждение"], "Person or group that has certified this dashboard.": ["Лицо или группа, которые утвердили этот дашборд"], "Any additional detail to show in the certification tooltip.": ["Любые дополнительные сведения для всплывающей подсказки."], "A list of tags that have been applied to this chart.": ["Список тегов, которые были применены к этому графику."], "You can now edit the colors of the metrics in a separate modal window on the dashboard by clicking on the ‘Edit label colours’ button.": ["Теперь вы можете редактировать цвета метрик в отдельном модальном окне на дашборде, нажав на кнопку «Редактировать цвета меток»."], "JSON metadata": ["JSON метаданные"], "Please DO NOT overwrite the \"filter_scopes\" key.": ["Пожалуйста, не перезаписывайте клавишу \\ \"filter_scopes \"."], "Use \"%(menuName)s\" menu instead.": ["Использовать меню \"%(menuName)s\" взамен."], "This dashboard is not published, it will not show up in the list of dashboards. Click here to publish this dashboard.": ["Этот дашборд не опубликован, он не будет отображён в списке дашбордов. Нажмите, чтобы опубликовать этот дашборд."], "This dashboard is not published which means it will not show up in the list of dashboards. Favorite it to see it there or access it by using the URL directly.": ["Этот дашборд не опубликован, что означает, что он не будет отображён в списке дашбордов. Добавьте его в избранное, чтобы увидеть там или воспользуйтесь доступом по прямой ссылке."], "This dashboard is published. Click to make it a draft.": ["Дашборд опубликован. Нажмите, чтобы сделать черновиком."], "Unpublish": ["Снять с публикации"], "Publish": ["Опубликовать"], "Draft": ["Черновик"], "Publish dashboard": ["Опубликовать дашборд"], "Are you sure you want to publish this dashboard?": ["Вы уверены, что хотите опубликовать этот дашборд?"], "If you publish dashboard it will be accessible for all users.": ["Если вы опубликуете дашборд, он будет доступен всем пользователям."], "Unpublish dashboard": ["Снять с публикации дашборд"], "Are you sure you want to unpublish this dashboard?": ["Вы уверены, что хотите перевести этот дашборд в статус черновика?"], "If you unpublish dashboard it will be marked as draft and it will be accessible only for owners.": ["Если вы снимете дашборд с публикации, он будет помечен как черновик и будет доступен только владельцам."], "Annotation layers are still loading.": ["Слои аннотаций загружаются."], "One ore more annotation layers failed loading.": ["Один или несколько слоев аннотации не удалось загрузить."], "This chart applies cross-filters to charts whose datasets contain columns with the same name.": ["Этот график применяет кроссфильтры к графикам, датасеты которых содержат столбцы с тем же именем."], "Data refreshed": ["Данные обновлены"], "Cached %s": ["Добавлено в кэш %s"], "Fetched %s": ["Получено %s"], "Query %s: %s": ["Запрос %s: %s"], "Force refresh": ["Обновить"], "Hide chart description": ["Скрыть описание графика"], "Show chart description": ["Показать описание графика"], "Cross-filtering scoping": ["Область распространения кроссфильтров"], "View query": ["Показать SQL запрос"], "View as table": ["Показать в виде таблицы"], "Chart Data: %s": ["Данные графика: %s"], "Share chart by email": ["Поделиться графиком по email"], "Check out this chart: ": ["Посмотреть график: "], "Export to .CSV": ["Скачать .CSV файл"], "Export to Excel": ["Скачать Excel файл"], "Export to full .CSV": ["Экспорт в целый .CSV"], "Export to full Excel": ["Экспорт в полный Excel"], "Download as image": ["Сохранить как изображение"], "Something went wrong.": ["Что-то пошло не так."], "Search...": ["Поиск..."], "No filter is selected.": ["Не выбраны фильтры."], "Editing 1 filter:": ["Редактирование 1 фильтра:"], "Batch editing %d filters:": ["Множественное редактирование %d фильтров:"], "Configure filter scopes": ["Настроить область действия фильтра"], "There are no filters in this dashboard.": ["В этом дашборде нет фильтров."], "Expand all": ["Расширить все"], "Collapse all": ["Свернуть всё"], "An error occurred while opening Explore": ["Произошла ошибка при открытии режима исследования"], "Empty column": ["Пустой столбец"], "This markdown component has an error.": ["Этот компонент содержит ошибки."], "This markdown component has an error. Please revert your recent changes.": ["Этот компонент содержит ошибки. Пожалуйста, отмените последние изменения."], "Empty row": ["Пустой ряд"], "You can": ["Вы можете"], "create a new chart": ["создать новый график"], "or use existing ones from the panel on the right": ["или использовать уже существующие из панели справа"], "You can add the components in the": ["Вы можете добавить компоненты в"], "edit mode": ["режиме редактирования"], "Delete dashboard tab?": ["Удалить вкладку дашборда?"], "Deleting a tab will remove all content within it. You may still reverse this action with the": ["Удаление вкладки удалит все ее содержимое. Вы можете отменить это действие при помощи сочетания клавиш"], "undo": ["отмены"], "button (cmd + z) until you save your changes.": ["(CTRL + Z), пока вы не сохраните изменения."], "CANCEL": ["ОТМЕНА"], "Divider": ["Разделитель"], "Header": ["Заголовок"], "Text / Markdown": ["Текст / отметка"], "Tabs": ["Вкладки"], "background": ["Фон"], "Preview": ["Предпросмотр"], "Sorry, something went wrong. Try again later.": ["Извините, что-то пошло не так. Попробуйте еще раз позже."], "The screenshot is being generated. Please, do not leave the page.": ["Снимки экрана генерируются."], "The screenshot could not be downloaded. Please, try again later.": ["Скриншот нельзя загрузить."], "The screenshot has been downloaded.": ["Скриншот был загружен."], "Add/Edit Filters": ["Добавить/изменить фильтры"], "No filters are currently added to this dashboard.": ["Не применено ни одного фильтра к данному дашборду."], "No global filters are currently added": ["В настоящее время глобальные фильтры не добавляются"], "Click on \"+Add/Edit Filters\" button to create new dashboard filters": ["Нажмите кнопку \"+Добавить/Изменить фильтры\", чтобы создать новые фильтры на дашборде"], "All filters (%(filterCount)d)": ["Все фильтры (%(filterCount)d)"], "Filter sets (%(filterSetCount)d)": ["Сохраненные (%(filterSetCount)d)"], "Apply filters": ["Применить"], "Clear all": ["Очистить"], "Locate the chart": ["Навестись на график"], "Cross-filters": ["Кросс-фильтры"], "Add custom scoping": ["Добавьте пользовательскую обзор"], "All charts/global scoping": ["Все графики/глобальная область"], "Select chart": ["Выберите график"], "Cross-filtering is not enabled in this dashboard": ["Кроссфильтрация не включена на этом дашборде"], "Select the charts to which you want to apply cross-filters when interacting with this chart. You can select \"All charts\" to apply filters to all charts that use the same dataset or contain the same column name in the dashboard.": ["Выберите графики, к которым вы хотите применить кросс-фильтры при взаимодействии с этим графиком. Вы можете выбрать \"Все графики\", чтобы применить фильтры ко всем графикам, которые используют один и тот же датасет или содержат одно и то же имя столбца в дашборде."], "Select the charts to which you want to apply cross-filters in this dashboard. Deselecting a chart will exclude it from being filtered when applying cross-filters from any chart on the dashboard. You can select \"All charts\" to apply cross-filters to all charts that use the same dataset or contain the same column name in the dashboard.": ["Выберите графики, к которым вы хотите применить кросс-фильтры в этом дашборде. Отмена выбора графика исключит его из фильтрации при применении перекрестныхфильтров к любому графику на дашборде. Вы можете выбрать \"Все графики\", чтобы применить кроссфильтры ко всем графикам, которые используют один и тот же датасет или содержат один и тот же столбец имя в дашборде»"], "All charts": ["Все графики"], "Enable cross-filtering": ["Включить кроссфильтр"], "Orientation of filter bar": ["Ориентация фильтра"], "Vertical (Left)": ["Вертикально (слева)"], "Horizontal (Top)": ["Горизонтально (сверху)"], "More filters": ["Больше фильтров"], "No applied filters": ["Фильтры не применены"], "Applied filters: %s": ["Применены фильтры: %s"], "Cannot load filter": ["Невозможно загрузить фильтр"], "Filters out of scope (%d)": ["Неприменяемые фильтры (%d)"], "Dependent on": ["Зависит от"], "Filter only displays values relevant to selections made in other filters.": ["Фильтр отображает только значения, относящиеся к выборам, сделанным в других фильтрах."], "Scope": ["Область применения"], "Filter type": ["Тип фильтра"], "Title is required": ["Название обязательно"], "(Removed)": ["(Удалено)"], "Undo?": ["Отменить?"], "Add filters and dividers": ["Добавить фильтры и разделители"], "[untitled]": ["[без названия]"], "Cyclic dependency detected": ["Обнаружена циклическая зависимость"], "Add and edit filters": ["Создать/изменить фильтры"], "Column select": ["Выбор столбца"], "Select a column": ["Выберите столбец"], "No compatible columns found": ["Не найдено подходящих столбцов"], "No compatible datasets found": ["Совместимые наборы данных не найдено"], "Select a dataset": ["Выберите датасет"], "Value is required": ["Значение обязательно"], "(deleted or invalid type)": ["(удалено или невалидный тип)"], "Limit type": ["Тип ограничения"], "No available filters.": ["Нет доступных фильтров."], "Add filter": ["Добавить фильтр"], "Values are dependent on other filters": ["Значения зависят от других фильтров"], "Values selected in other filters will affect the filter options to only show relevant values": ["Значения, выбранные в других фильтрах, повлияют на параметры фильтра, чтобы показать только соответствующие значения"], "Values dependent on": ["Значения зависят от"], "Scoping": ["Область применения"], "Filter Configuration": ["Конфигурация фильтра"], "Filter Settings": ["Настройки фильтра"], "Select filter": ["Фильтр выбора"], "Range filter": ["Диа<PERSON>азон"], "Numerical range": ["Числовой диапазон"], "Time filter": ["Фильтр по времени"], "Time range": ["Фильтр по времени"], "Time column": ["Столбец даты/времени"], "Time grain": ["Единица времени"], "Group By": ["Группировать по"], "Group by": ["Группировать по"], "Select by id filter": ["Фильтр по ID"], "Filter by ID": ["Фильтр по ID"], "Select with translation": ["Фильтр выбора с переводом"], "Value with translation": ["Значение с переводом"], "Select by id with translation": ["Фильтр по ID с переводом"], "Filter by ID with translation": ["Фильтр по ID c переводом"], "This filter is used if there are two columns with values in different languages. For example, Delivery and Доставка": ["Данный фильтр используется, если существуют две колонки со значениями на разных языках. Например, Delivery и Доставка"], "Dashboard time range filters apply to temporal columns in chart filters. Add temporal columns to apply the dashboard filter": ["Фильтры по интервалу времени применяются к временным столбцам в фильтрах графиков. Добавьте временные столбцы для применения фильтра"], "Pre-filter is required": ["Предварительная фильтрация обязательна"], "Time column to apply dependent temporal filter to": ["Столбец времени для применения зависимого временного фильтра к"], "Time column to apply time range to": ["Временный столбец для применения диапазона времени к"], "Name is required": ["Имя обязательно"], "Filter Type": ["Тип фильтра"], "Datasets do not contain a temporal column": ["Датасет не содержит столбца формата дата/время"], "Filter name": ["Имя фильтра"], "Dataset is required": ["Требуется датасет"], "Use it with caution": ["Используйте с осторожностью"], "Not a valid integer": ["Не целое число"], "Pre-filter available values": ["Предварительно выбрать доступные значения для фильтра"], "Add filter clauses to control the filter's source query,\n                    though only in the context of the autocomplete i.e., these conditions\n                    do not impact how the filter is applied to the dashboard. This is useful\n                    when you want to improve the query's performance by only scanning a subset\n                    of the underlying data or limit the available values displayed in the filter.": ["Добавьте предложения фильтра, чтобы управлять исходным запросом фильтра, хотя только в контексте автозаполнения, то есть эти условия \\ n не влияют на то, как фильтр применяется к панели панели."], "Pre-filter": ["Предварительная фильтрация"], "No filter": ["Без фильтра"], "Sort filter values": ["Сортировать отфильтрованные значения"], "Sort type": ["Тип сортировки"], "Sort ascending": ["Сортировать по возрастанию"], "Sort Metric": ["Мера для сортировки"], "If a metric is specified, sorting will be done based on the metric value": ["Если мера задана, сортировка будет произведена на основании значений меры"], "Sort metric": ["Показатель для сортировки"], "Single Value": ["Одиночное значение"], "Single value type": ["Тип одиночного значения"], "Exact": ["Точное"], "Filter has default value": ["Фильтр имеет значение по умолчанию"], "Default Value": ["Значение по умолчанию"], "Default value is required": ["Требуется значение по умолчанию"], "Refresh the default values": ["Обновить значения по умолчанию"], "Fill all required fields to enable \"Default Value\"": ["Укажите все необходимые поля"], "You have removed this filter.": ["Вы удалили фильтр."], "Restore Filter": ["Восстановить фильтр"], "ColumnId": ["Columnid"], "Column is required": ["Столбец обязателен"], "Populate \"Default value\" to enable this control": ["Заполнение \\ \"Значение по умолчанию \""], "Default value set automatically when \"Select first filter value by default\" is checked": ["Значение по умолчанию задается автоматически, когда установлен флаг \"Сделать первое значение фильтра значением по умолчанию\""], "Default value must be set when \"Filter value is required\" is checked": ["Значение по умолчанию должно быть выбрано, когда установлен флаг \"Требуется значение фильтра\""], "Default value must be set when \"Filter has default value\" is checked": ["Значение по умолчанию должно быть выбрано, когда установлен флаг \"Фильтр имеет значение по умолчанию\""], "Apply to all panels": ["Применить ко всем панелям"], "Apply to specific panels": ["Применить к выбранным панелям"], "Only selected panels will be affected by this filter": ["Фильтр будет применён только к выбранным панелям"], "All panels with this column will be affected by this filter": ["Фильтр будет применён ко всем панелям с этим столбцом"], "All panels": ["Все панели"], "This chart might be incompatible with the filter (datasets don't match)": ["Этот график может быть несовместим с этим фильтром (датасеты не совпадают)"], "Keep editing": ["Продолжить редактирование"], "Yes, cancel": ["Да, отменить"], "There are unsaved changes.": ["У вас есть несохраненные изменения."], "Are you sure you want to cancel?": ["Вы уверены, что хотите отменить?"], "Error loading chart datasources. Filters may not work correctly.": ["Ошибка загрузки источников данных для графиков. Фильтры могут работать некорректно."], "Error loading chart filter sets. Primary filter set won't be applied.": ["Наборы фильтров с ошибкой загрузки."], "Transparent": ["Прозрачный"], "White": ["Белый"], "All filters": ["Все фильтры"], "Click to edit %s.": ["Нажмите для редактирования %s."], "Click to edit chart.": ["Нажмите для редактирования графика."], "Use %s to open in a new tab.": ["Используйте %s для открытия в отдельной вкладке."], "Medium": ["Средний"], "New header": ["Новый заголовок"], "Tab title": ["Имя вкладки"], "This page is intended to be embedded in an iframe, but it looks like that is not the case.": ["Эта страница предназначена для включения в iframe, но похоже, что это не так."], "This session has encountered an interruption, and some controls may not work as intended. If you are the developer of this app, please check that the guest token is being generated correctly.": ["Эта сессия столкнулась с перерывом, и некоторые элементы управления могут не работать, как предполагалось."], "Something went wrong with embedded authentication. Check the dev console for details.": ["Что -то пошло не так со встроенной аутентификацией."], "Equal to (=)": ["Равен (=)"], "Not equal to (≠)": ["Не равно (≠)"], "Less than (<)": ["Меньше, чем (<)"], "Less or equal (<=)": ["Меньше или равных (<=)"], "Greater than (>)": ["Больше, чем (>)"], "Greater or equal (>=)": ["Больше или равен (> =)"], "In": ["В"], "Not in": ["не в"], "Like": ["Как"], "Like (case insensitive)": ["Like (нечувствительна к регистру)"], "Is not null": ["не нулевой"], "Is null": ["Это null"], "use latest_partition template": ["используйте шаблон latest_partition"], "Is true": ["Это правда"], "Is false": ["Это false"], "TEMPORAL_RANGE": ["TEMPORAL_RANGE"], "Time granularity": ["Гранулярность времени"], "One or many columns to group by. High cardinality groupings should include a series limit to limit the number of fetched and rendered series.": ["один или много столбцов для группы. Один или несколько столбцов для группировки. Столбцы с множеством уникальных значений должны включать порог количества категорий для снижения нагрузку на базу данных и на ускорения отображения графика."], "One or many metrics to display": ["Выберите одну или несколько мер для отображения"], "Fixed color": ["Фиксированный цвет"], "Right axis metric": ["Мера для правой оси"], "Choose a metric for right axis": ["Выберите меру для правой оси"], "Linear color scheme": ["Линейная цветовая схема"], "Color metric": ["Мера для цвета"], "One or many controls to pivot as columns": ["один или много элементов управления Pivot в качестве столбцов"], "The time granularity for the visualization. Note that you can type and use simple natural language as in `10 seconds`,`1 day` or `56 weeks`": ["Время гранулярность для визуализации. Интервал времени, в границах которого строится график. Обратите внимание, что для определения диапазона времени, вы можете использовать естественный язык. Например, можно указать словосочетания - «10 seconds», «1 day» или «56 weeks»"], "The time granularity for the visualization. This applies a date transformation to alter your time column and defines a new time granularity. The options here are defined on a per database engine basis in the Superset source code.": ["Время гранулярности для визуализации. Детализация времени для визуализации. Применяется преобразование столбца с датой/временем и определяется новая детализация (минута, день, год, и т.п.). Доступные варианты заданы в исходном коде Superset для каждого типа драйвера базы данных."], "The time range for the visualization. All relative times, e.g. \"Last month\", \"Last 7 days\", \"now\", etc. are evaluated on the server using the server's local time (sans timezone). All tooltips and placeholder times are expressed in UTC (sans timezone). The timestamps are then evaluated by the database using the engine's local timezone. Note one can explicitly set the timezone per the ISO 8601 format if specifying either the start and/or end time.": ["Диапазон времени для визуализации. Все относительные времена, например «Последний месяц», «Последние 7 дней», «Сейчас» и т. д., оцениваются на сервере по местному времени сервера (без учета часового пояса). Все всплывающие подсказки и временные метки выражаются в UTC (без учета часового пояса). Временные метки затем оцениваются базой данных с использованием локального часового пояса движка. Обратите внимание, что при указании начального и/или конечного времени можно явно задать часовой пояс в соответствии с форматом ISO 8601."], "Limits the number of rows that get displayed.": ["Ограничивает количество отображаемых строк"], "Metric used to define how the top series are sorted if a series or row limit is present. If undefined reverts to the first metric (where appropriate).": ["Мера, используемая для определения того, как сортируются верхние категории, если присутствует ограничение по категории или строке. Если не определено, возвращается к первой мере (где это уместно)."], "Defines the grouping of entities. Each series is shown as a specific color on the chart and has a legend toggle": ["Группировка в ряды данных. Каждая категория отображается в виде определенного цвета на графике и имеет легенду"], "Metric assigned to the [X] axis": ["Показатель, отраженный на оси X"], "Metric assigned to the [Y] axis": ["Показатель, отраженный на оси Y"], "Bubble size": ["Размер маркера"], "When `Calculation type` is set to \"Percentage change\", the Y Axis Format is forced to `.1%`": ["Когда `Тип расчёта` установлен в \"Процентное изменение\", формат оси Y устанавливается в `.1%`"], "An error occurred while starring this chart": ["Произошла ошибка при добавлении графика в избранное"], "Chart [%s] has been saved": ["График [%s] сохранен"], "Chart [%s] has been overwritten": ["График [%s] перезаписан"], "Dashboard [%s] just got created and chart [%s] was added to it": ["Дашборд [%s] был только что создан и график [%s] был добавлен в него"], "Chart [%s] was added to dashboard [%s]": ["График [%s] добавлен в дашборд [%s]"], "GROUP BY": ["GROUP BY"], "Use this section if you want a query that aggregates": ["Используйте этот раздел, если вам нужен запрос, который агрегирует"], "NOT GROUPED BY": ["не сгруппировано"], "Use this section if you want to query atomic rows": ["Используйте этот раздел, если вы хотите запросить атомные ряды"], "Advanced analytics post processing": ["Advanced Analytics после обработки"], "Shift start date": ["Дата начала смены"], "The X-axis is not on the filters list": ["Ось X не в списке фильтров."], "The X-axis is not on the filters list which will prevent it from being used in\n            time range filters in dashboards. Would you like to add it to the filters list?": ["Ось X не находится в списке фильтров, который предотвратит его использование\n            в фильтрах временного диапазона на дашбордах."], "You cannot delete the last temporal filter as it's used for time range filters in dashboards.": ["Вы не можете удалить последний височный фильтр, поскольку он используется для фильтров диапазона времени на панели мониторинга."], "This section contains validation errors": ["В этом разделе содержатся ошибки валидации"], "Keep control settings?": ["Оставить прежние настройки?"], "You've changed datasets. Any controls with data (columns, metrics) that match this new dataset have been retained.": ["Вы изменили датасеты. Все элементы управления с данными (столбцами, мерами), которые соответствуют новому датасету, были сохранены."], "Continue": ["Продолжить"], "Clear form": ["Очистить форму"], "No form settings were maintained": ["Конфигурация графика не сохранилась"], "We were unable to carry over any controls when switching to this new dataset.": ["Не удалось перенести настройки прошлого графика при переключении датасета."], "Data": ["Данные"], "Customize": ["Кастомизация"], "Generating link, please wait..": ["Генерация ссылки, пожалуйста, ждите..."], "Chart height": ["Высота графика"], "Chart width": ["<PERSON>и<PERSON>ина графика"], "An error occurred while loading dashboard information.": ["Произошла ошибка при загрузке информации о панели инструментов."], "Save (Overwrite)": ["Сохранить (Перезаписать)"], "Save as...": ["Сохранить как..."], "Chart name": ["Имя графика"], "Dataset Name": ["Имя датасета"], "A reusable dataset will be saved with your chart.": ["Переиспользуемый датасет будет сохранен с вашим графиком."], "Add to dashboard": ["Добавить в дашборд"], "Select a dashboard": ["Выбрать дашборд"], "Select": ["Выбрать"], " a dashboard OR ": [" дашборд или "], "create": ["создать"], " a new one": [" новый"], "A new chart and dashboard will be created.": ["Будет создан новый график и дашборд."], "A new chart will be created.": ["Будет создан новый график и дашборд."], "A new dashboard will be created.": ["Будет создан новый дашборд."], "Save & go to dashboard": ["Сохранить и перейти к дашборду"], "Save chart": ["Сохранить график"], "Formatted date": ["Форматированная дата"], "Column Formatting": ["Форматирование столбца(ов)"], "Collapse data panel": ["Свернуть панель управления"], "Expand data panel": ["Расширить панель данных"], "Samples": ["Примеры данных"], "No samples were returned for this dataset": ["Не было получено данных для этого датасета"], "No results": ["Нет результатов"], "Showing %s of %s": ["Отображено %s из %s"], "%s ineligible item(s) are hidden": ["%s неконкулируемые предметы (ы) скрыты"], "Show less...": ["Показать меньше..."], "Show all...": ["Показать все..."], "Search Metrics & Columns": ["Поиск по мерам и столбцам"], "Create a dataset": ["Создать датасет"], " to edit or add columns and metrics.": [" для редактирования или добавления столбцов и мер."], "Unable to retrieve dashboard colors": ["Не удалось получать цветовую схему дашборда"], "Added to 1 dashboard": ["Добавлено к 1 дашборд"], "Not added to any dashboard": ["Не добавлен ни в один дашборд"], "You can preview the list of dashboards in the chart settings dropdown.": ["Вы можете предварительно просмотреть список панелей мониторинга в раскрывающемся списке настройки графика."], "Add the name of the chart": ["Задайте имя графика"], "Chart title": ["Название графика"], "Add required control values to save chart": ["Добавьте обязательные значения для сохранения графика"], "Chart type requires a dataset": ["Для данного типа графика необходим датасет"], "This chart type is not supported when using an unsaved query as a chart source. ": ["Этот тип графика не поддерживается при использовании несохраненного запроса в качестве источника графика."], " to visualize your data.": [" для визуализации ваших данных."], "Required control values have been removed": ["Обязательные значения были удалены"], "Your chart is not up to date": ["Ваш график не актуален"], "You updated the values in the control panel, but the chart was not updated automatically. Run the query by clicking on the \"Update chart\" button or": ["Вы обновили значения в панели управления, но график не был обновлен автоматически. Сделайте запрос, нажав на кнопку \"Обновить график\" или"], "Controls labeled ": ["Значения с именами "], "Control labeled ": ["Значение с именем "], "Chart Source": ["Источник данных"], "Open Datasource tab": ["Открыть вкладку источника данных"], "Original": ["Исходные данные"], "You do not have permission to edit this chart": ["У вас нет прав на редактирование этого графика"], "Chart properties updated": ["Свойства графика обновлены"], "Edit Chart Properties": ["Редактировать свойства графика"], "This chart is managed externally, and can't be edited in Superset": ["Этот график управляется внешне и не может быть отредактирован в Superset"], "The description can be displayed as widget headers in the dashboard view. Supports markdown.": ["Описание может быть отображено как заголовок графика в дашборде. Поддерживает markdown-разметку"], "Person or group that has certified this chart.": ["Лицо или группа, которые утвердили этот график"], "Configuration": ["Конфигурация"], "Duration (in seconds) of the caching timeout for this chart. Set to -1 to bypass the cache. Note this defaults to the dataset's timeout if undefined.": ["Длительность (в секундах) таймаута кэширования для этого графика. Установите значение -1, чтобы обойти кэш. Если значение не определено, по умолчанию используется таймаут набора данных."], "A list of users who can alter the chart. Searchable by name or username.": ["Список пользователей, которые могут изменять график. Можно искать по имени или имени пользователя."], "The row limit set for the chart was reached. The chart may show partial data.": ["Достигнут лимит строк, установленный для графика. На графике могут отображаться неполные данные."], "Create chart": ["Создать график"], "Update chart": ["Обновить график"], "Actual range for comparison": ["Фактический диапазон для сравнения"], "Invalid lat/long configuration.": ["Неверная конфигурация широты и долготы."], "Reverse lat/long ": ["Поменять местами широту и долготу "], "Longitude & Latitude columns": ["Долгота и Широта"], "Delimited long & lat single column": ["Долгота и широта в одном столбце"], "Multiple formats accepted, look the geopy.points Python library for more details": ["Для уточнения форматов и получения более подробной информации, посмотрите Python-библиотеку geopy.points"], "Geohash": ["<PERSON><PERSON><PERSON><PERSON>"], "textarea": ["текстовая область"], "in modal": ["в модальном окне"], "Sorry, An error occurred": ["Извините, произошла ошибка"], "Save as Dataset": ["Сохранить как датасет"], "Open in SQL Lab": ["Открыть в SQL редакторе"], "Failed to verify select options: %s": ["Ошибка при проверке вариантов выбора: %s"], "No annotation layers": ["Нет слоев аннотаций"], "Add an annotation layer": ["Добавить слой аннотации"], "Annotation layer": ["Слой аннотаций"], "Select the Annotation Layer you would like to use.": ["Выбрать слой аннотации, который вы хотите использовать."], "Use another existing chart as a source for annotations and overlays.\n          Your chart must be one of these visualization types: [%s]": ["Используйте другой существующий график в качестве источника для аннотаций и наложений.\n          Ваш график должен быть одним из этих типов визуализации: [%s]"], "Expects a formula with depending time parameter 'x'\n        in milliseconds since epoch. mathjs is used to evaluate the formulas.\n        Example: '2x+5'": ["Формула с зависимой переменной 'x' в милисекундах с 1970 года (Unix-время). Для рассчета используется mathjs. Например: '2x+5'"], "Annotation layer value": ["Значение слоя аннотации"], "Bad formula.": ["Неверная формула."], "Annotation Slice Configuration": ["Настройки аннотации из графика"], "This section allows you to configure how to use the slice\n              to generate annotations.": ["Этот раздел позволяет настроить, как использовать Slice \\ N для генерации аннотаций."], "Annotation layer time column": ["Столбец времени Аннотации Время времени"], "Interval start column": ["Столбец с началом интервала"], "Event time column": ["Столбец формата дата/время"], "This column must contain date/time information.": ["В этом столбец должны быть данные формата дата/время."], "Annotation layer interval end": ["Конечный интервал слоя аннотации"], "Interval End column": ["Столбец с концом интервала"], "Annotation layer title column": ["Столбец заголовка аннотации слоя"], "Title Column": ["Столбец с названием"], "Pick a title for you annotation.": ["Выберите название для вашей аннотации"], "Annotation layer description columns": ["Описательные столбцы слоя аннотаций."], "Description Columns": ["Описательные столбцы"], "Pick one or more columns that should be shown in the annotation. If you don't select a column all of them will be shown.": ["Выберите один или несколько столбцов, которые должны отображаться в аннотации. Если вы не выберите столбец, все столбцы будут отображены."], "Override time range": ["Переопределить временной интервал"], "This controls whether the \"time_range\" field from the current\n                  view should be passed down to the chart containing the annotation data.": ["Должен ли временной интервал из этого представления переписать временной интервал графика, содержащего данные аннотации."], "Override time grain": ["Переопределить единицу времени"], "This controls whether the time grain field from the current\n                  view should be passed down to the chart containing the annotation data.": ["Должен ли единица времени из этой таблицы переписать единицу времени графика."], "Time delta in natural language\n                  (example:  24 hours, 7 days, 56 weeks, 365 days)": ["Временной сдвиг на естественном языке (например: 24 hours, 7 days, 56 weeks, 365 days)"], "Display configuration": ["Настройки отображения"], "Configure your how you overlay is displayed here.": ["Настройка отображения слоя аннотации поверх графика."], "Annotation layer stroke": ["Штрих слоя аннотации"], "Style": ["Стиль"], "Solid": ["Сплошной"], "Dashed": ["Штрих"], "Long dashed": ["Длинный штрих"], "Dotted": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "Annotation layer opacity": ["Непрозрачность слоя аннотации"], "Automatic Color": ["Автоматический цвет"], "Shows or hides markers for the time series": ["Показывает или скрывает маркеры для временных рядов"], "Hide Line": ["Скрыть линию"], "Hides the Line for the time series": ["Скрывает линию для временных рядов"], "Layer configuration": ["Настройки слоя"], "Configure the basics of your Annotation Layer.": ["Настройте слой аннотации."], "Mandatory": ["Обязательно"], "Hide layer": ["Скрыть слой"], "Show label": ["Показывать метку"], "Whether to always show the annotation label": ["Всегда показывать метку аннотации"], "Annotation layer type": ["Тип слоя аннотации"], "Choose the annotation layer type": ["Выбрать тип слоя аннотации"], "Annotation source type": ["Тип источника аннотации"], "Choose the source of your annotations": ["Выберите источник аннотаций"], "Annotation source": ["Источник аннотации"], "Remove": ["Удалить"], "Time series": ["Временной ряд"], "Edit annotation layer": ["Редактировать слой аннотации"], "Add annotation layer": ["Добавить слой аннотации"], "Empty collection": ["Пустая коллекция"], "Add an item": ["Добавить запись"], "Remove item": ["Удалить элемент"], "The colors of this chart might be overridden by custom label colors of the related dashboard.\n    Check the JSON metadata in the Advanced settings.": ["Цвета этого графика могут быть переопределены по индивидуальным цветам метки соответствующего дашборда.\n    Проверьте метаданные JSON в расширенных настройках."], "The color scheme is determined by the related dashboard.\n        Edit the color scheme in the dashboard properties.": ["Цветовая схема определяется соответствующим дашбордом.\n        Измените цветовую схему в свойствах дашборда."], "You are viewing this chart in a dashboard context with labels shared across multiple charts.\n        The color scheme selection is disabled.": ["Вы просматриваете этот график в контексте дашборда с названиями, разделенными по нескольким графикам.\n        Выбор цветовой схемы отключен."], "You are viewing this chart in the context of a dashboard that is directly affecting its colors.\n        To edit the color scheme, open this chart outside of the dashboard.": ["Вы просматриваете этот график в контексте дашборда, который напрямую влияет на его цвета.\n        Чтобы изменить цветовую схему, откройте этот график за пределами дашборда."], "Dashboard scheme": ["Схема дашборда"], "Custom color palettes": ["Пользовательские цветовые палитры"], "Featured color palettes": ["Показанные цветовые палитры"], "Other color palettes": ["Другие цветовые палитры"], "Select color scheme": ["Выберите цветовую схему"], "Select scheme": ["Выберите схему"], "Show less columns": ["Показать меньше столбцов"], "Show all columns": ["Показать все столбцы"], "This metric will be exported as time": ["Эта мера будет экспортирована в формате времени"], "Fraction digits": ["Десятичные знаки"], "Number of decimal digits to round numbers to": ["Кол-во десятичных разрядов для округления числа"], "Min Width": ["Минимальная ширина"], "Default minimal column width in pixels, actual width may still be larger than this if other columns don't need much space": ["Минимальная ширина столбца (в пикселях). Реальная ширина по-прежнему может быть больше, чем указанная, если остальным столбцам не будет хватать места"], "Text align": ["Выравнивание текста"], "Horizontal alignment": ["Выравнивание по горизонтали"], "Show cell bars": ["Наложить гистограммы на ячейки"], "Whether to align positive and negative values in cell bar chart at 0": ["Выравнивание гистограммы внутри ячеек по горизонтали слева"], "Color +/-": ["Раскрасить +/-"], "Whether to colorize numeric values by if they are positive or negative": ["Окрашивать ячейки с числами в зависимости от их знака"], "Truncate Cells": ["Усеченные клетки"], "Truncate long cells to the \"min width\" set above": ["Усечение длинных ячеек до «минимальной ширины», установленной выше"], "Pin column": ["Закрепить колонку"], "Pin column with horizontal scroll": ["Закрепить колонку при горизонтальном скроле"], "Select aggregate options": ["Выберите настройки агрегации"], "%s aggregates(s)": ["Агрегатных функций: %s"], "Hide value in Total": ["Скрыть значение в Итого"], "Customize chart metrics or columns with currency symbols as prefixes or suffixes. Choose a symbol from dropdown or type your own.": ["Настройте метрики или столбцы графика с символами валюты в качестве префиксов или суффиксов."], "Small number format": ["Форматирование маленьких чисел"], "D3 number format for numbers between -1.0 and 1.0, useful when you want to have different significant digits for small and large numbers": ["формат номеров D3 для чисел от -1,0 до 1,0, полезный, если вы хотите иметь различные значимые цифры для небольших и больших чисел"], "No gradient": ["Нет градиента"], "Color: ": ["Цвет: "], "Lower threshold must be lower than upper threshold": ["Нижний порог должен быть ниже, чем верхний порог"], "Upper threshold must be greater than lower threshold": ["Верхний порог должен быть больше нижнего порога"], "Isoline": ["изолиния"], "Threshold": ["Порог"], "Defines the value that determines the boundary between different regions or levels in the data ": ["определяет значение, которое определяет границу между различными регионами или уровнями в данных "], "The width of the Isoline in pixels": ["ширина изолина в пикселях"], "The color of the isoline": ["цвет изолинии"], "Isoband": ["Изобанд"], "Lower Threshold": ["Нижний порог"], "The lower limit of the threshold range of the Isoband": ["Нижний предел порогового диапазона изобанд"], "Upper Threshold": ["Верхний порог"], "The upper limit of the threshold range of the Isoband": ["верхний предел порогового диапазона изобанд"], "The color of the isoband": ["Цвет изолинии"], "Click to add a contour": ["Нажмите, чтобы добавить контур"], "Prefix": ["Префикс"], "Suffix": ["Суффикс"], "Currency prefix or suffix": ["Префикс или суффикс валюты"], "Prefix or suffix": ["Префикс или суффикс"], "Currency symbol": ["Символ валюты"], "Currency": ["Валюта"], "Edit dataset": ["Редактировать датасет"], "You must be a dataset owner in order to edit. Please reach out to a dataset owner to request modifications or edit access.": ["Вы должны быть владельцем датасета для его редактирования. Пожалуйста, обратитесь к владельцу с просьбой предоставить доступ."], "View in SQL Lab": ["Открыть в Лаборатории SQL"], "Query preview": ["Предпросмотр запроса"], "Save as dataset": ["Сохранить как датасет"], "Missing URL parameters": ["Пропущенные параметры URL"], "The URL is missing the dataset_id or slice_id parameters.": ["URL -адрес отсутствует параметры dataset_id или slice_id."], "The dataset linked to this chart may have been deleted.": ["Дата<PERSON><PERSON>т, связанный с этим графиком, похоже, был удален."], "RANGE TYPE": ["Тип"], "Actual time range": ["Текущие настройки фильтрации времени"], "APPLY": ["ПРИМЕНИТЬ"], "Edit time range": ["Изменить временной интервал"], "Configure Advanced Time Range ": ["Установить особый временной интервал "], "START (INCLUSIVE)": ["НАЧАЛО (ВКЛЮЧИТЕЛЬНО)"], "Start date included in time range": ["Начальная дата включена во временной интервал"], "END (EXCLUSIVE)": ["КОНЕЦ (НЕ ВКЛЮЧИТЕЛЬНО)"], "End date excluded from time range": ["Конечная дата исключена из временного интервала"], "Configure Time Range: Previous...": ["Установить временной интервал: предыдущий..."], "Configure Time Range: Last...": ["Установить временной интервал: последний..."], "Configure Time Range: Current...": ["Настройте диапазон времени: текущий ..."], "Configure custom time range": ["Установить пользовательский временной интервал"], "Relative quantity": ["Относительное количество"], "Relative period": ["Относительный период"], "END (INCLUSIVE)": ["КОНЕЦ (ВКЛЮЧИТЕЛЬНО)"], "End date included to time range": ["Дата окончания включена в диапазон времени"], "Anchor to": ["Привязать к"], "NOW": ["Сей<PERSON><PERSON>с"], "Date/Time": ["Дата/Время"], "Return to specific datetime.": ["Вернуться к конкретной дате."], "Syntax": ["Син<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "Example": ["Пример"], "Moves the given set of dates by a specified interval.": ["Перемещает заданный набор дат с указанным интервалом."], "Truncates the specified date to the accuracy specified by the date unit.": ["Усекает указанную дату с точностью, указанной в единице измерения даты."], "Get the last date by the date unit.": ["Получить последнюю дату от подразделения даты."], "Get the specify date for the holiday": ["Получить дату указания на праздник"], "Previous": ["Предыдущий"], "Current": ["Текущий"], "Select date (until included)": ["Выберите дату (включая конец)"], "Last day": ["Последние сутки"], "Last week": ["Последние 7 дней"], "Last month": ["Последние 30 дней"], "Last quarter": ["Последние 90 дней"], "Last year": ["Последние 365 дней"], "previous calendar week": ["предыдущая календарная неделя"], "previous calendar month": ["предыдущий календарный месяц"], "previous calendar year": ["предыдущий календарный год"], "Current day": ["Текущий день"], "Current week": ["Текущая неделя"], "Current month": ["Текущий месяц"], "Current quarter": ["Текущий квартал"], "Current year": ["Текущий год"], "Seconds %s": ["Секунд %s"], "Minutes %s": ["Минут %s"], "Hours %s": ["Часов %s"], "Days %s": ["Дней %s"], "Weeks %s": ["Недель %s"], "Months %s": ["Месяцев %s"], "Quarters %s": ["Кварталов %s"], "Years %s": ["Лет %s"], "Specific Date/Time": ["Конкретная дата/время"], "Relative Date/Time": ["Относительная дата/время"], "Now": ["Сей<PERSON><PERSON>с"], "Midnight": ["Полночь"], "Saved expressions": ["Сохраненные выражения"], "Saved": ["Сохранено"], "%s column(s)": ["Столбцов: %s"], "No temporal columns found": ["Столбцы формата дата/время не найдены"], "No saved expressions found": ["Не найдено сохраненных выражений"], "Add calculated temporal columns to dataset in \"Edit datasource\" modal": ["Добавьте новые столбцы формата дата/время в датасет в настройках датасета"], "Add calculated columns to dataset in \"Edit datasource\" modal": ["Добавьте новые вычисляемые столбцы в датасет в настройках датасета"], " to mark a column as a time column": [", чтобы пометить столбец как столбец даты/времени"], " to add calculated columns": [" для добавления вычисляемых столбцов"], "Simple": ["Столбец"], "Mark a column as temporal in \"Edit datasource\" modal": ["Присвойте столбцу формат даты/времени в настройках датасета"], "Custom SQL": ["Ч<PERSON>р<PERSON>з SQL"], "My column": ["Мой столбец"], "This filter might be incompatible with current dataset": ["Этот фильтр может быть несовместим с этим датасетом"], "This column might be incompatible with current dataset": ["Этот график может быть несовместим с этим датасетом"], "Drop a column here or click": ["Отбросьте столбец здесь или нажмите"], "Click to edit label": ["Нажмите для редактирования метки"], "Drop columns/metrics here or click": ["Перетащите столбцы/меры сюда"], "This metric might be incompatible with current dataset": ["Эта мера может быть несовместима с этим датасетом"], "Drop a column/metric here or click": ["Перетащите столбец/меру сюда"], "\n                This filter was inherited from the dashboard's context.\n                It won't be saved when saving the chart.\n              ": ["\nЭтот фильтр был унаследован от контекста панели.\n                Фильтр был наследован из контекста дашборда.\n                Он не будет сохранен при сохранении графика.\n              "], "%s option(s)": ["%s вариант(ов)"], "Select subject": ["Выберите Тема"], "No such column found. To filter on a metric, try the Custom SQL tab.": ["Такой столбец не найден. Чтобы фильтровать по мере, попробуйте использовать вкладку Свой SQL."], "To filter on a metric, use Custom SQL tab.": ["Для фильтрации по мере используйте вкладку Свой SQL."], "%s operator(s)": ["%s параметр(ы)"], "Select operator": ["Выбрать оператор"], "Comparator option": ["Вариант сравнения"], "Type a value here": ["Введите значение здесь"], "Filter value (case sensitive)": ["Фильтровать значения (зависит от регистра)"], "Failed to retrieve advanced type": ["Не удалось получить расширенный тип"], "choose WHERE or HAVING...": ["выберите WHERE или HAVING..."], "Filters by columns": ["Фильтры по столбцам"], "Filters by metrics": ["Фильтры по мерам"], "Fixed": ["Фиксированный"], "Based on a metric": ["На основе меры"], "My metric": ["Моя мера"], "Add metric": ["Добавить меру"], "Select saved metrics": ["Выберите сохраненные меры"], "%s saved metric(s)": ["Сохраненная мер: %s"], "Saved metric": ["Сохраненная мера"], "No saved metrics found": ["Не найдено сохраненных мер"], "Add metrics to dataset in \"Edit datasource\" modal": ["Добавьте меры в датасет в настройках датасета"], " to add metrics": [" для добавления мер"], "Simple ad-hoc metrics are not enabled for this dataset": ["Простые специальные метрики не включены для этого набора данных"], "column": ["столбец"], "aggregate": ["агрегатная функция"], "Custom SQL ad-hoc metrics are not enabled for this dataset": ["Пользовательские ad-hoc меры SQL не разрешены для этого датасета"], "Error while fetching data: %s": ["Возникла ошибка при получении данных: %s"], "Time series columns": ["Столбцы временных рядов"], "Actual value": ["Фактическое значение"], "Sparkline": ["Спарклайн"], "Period average": ["Среднее за период"], "The column header label": ["Заголовок столбца"], "Column header tooltip": ["Всплывающая подсказка заголовка столбца"], "Type of comparison, value difference or percentage": ["Тип сравнения, разница значений или доля"], "Width": ["Ши<PERSON><PERSON><PERSON>"], "Width of the sparkline": ["<PERSON><PERSON><PERSON><PERSON>на спарклайна"], "Height of the sparkline": ["Высота спарклайна"], "Time lag": ["Временной лаг"], "Number of periods to compare against. You can use negative numbers to compare from the beginning of the time range.": ["Количество периодов для сравнения с."], "Time Lag": ["Временной лаг"], "Time ratio": ["Соотношение времени"], "Number of periods to ratio against": ["Количество периодов для сравнения"], "Time Ratio": ["Соотношение времени"], "Show Y-axis": ["Показать ось Y"], "Show Y-axis on the sparkline. Will display the manually set min/max if set or min/max values in the data otherwise.": ["Показывать ось Y на спарклайне."], "Y-axis bounds": ["Границы оси Y"], "Manually set min/max values for the y-axis.": ["Вручную задать мин./макс. значения для оси Y"], "Color bounds": ["Границы цвета"], "Number bounds used for color encoding from red to blue.\n               Reverse the numbers for blue to red. To get pure red or blue,\n               you can enter either only min or max.": ["Ограничения чисел, используемые для кодирования цвета от красного до синего. \\ N Обратите число чисел для синего до красного."], "Optional d3 number format string": ["Формат числовой строки"], "Number format string": ["Числовой формат"], "Optional d3 date format string": ["Формат временной строки"], "Date format string": ["Формат временной строки"], "Column Configuration": ["Свойства столбца"], "Select Viz Type": ["Выберите тип визуализации"], "Currently rendered: %s": ["Сейчас отрисовано: %s"], "Other": ["Прочее"], "Search all charts": ["Поиск по всем графикам"], "No description available.": ["Описание отсутствует."], "Examples": ["Примеры"], "This visualization type is not supported.": ["Этот тип визуализации не поддерживается."], "View all charts": ["Показать все графики"], "Select a visualization type": ["Выберите тип визуализации"], "No results found": ["Записи не найдены"], "Superset Chart": ["График Superset"], "New chart": ["Новый график"], "Edit chart properties": ["Редактировать свойства графика"], "On dashboards": ["На дашбордах"], "Export to original .CSV": ["Экспорт исходных данных в .CSV"], "Export to .JSON": ["Экспорт в .JSON"], "Embed code": ["Встроенный код"], "Run in SQL Lab": ["Открыть в SQL редакторе"], "Code": ["Редактор"], "Markup type": ["Тип разметки"], "Pick your favorite markup language": ["Выберите свой любимый язык разметки"], "Put your code here": ["Введите произвольный текст в формате html или markdown"], "URL parameters": ["Параметры URL"], "Extra parameters for use in jinja templated queries": ["Дополнительные параметры для запросов, использующих шаблонизацию Jinja"], "Annotations and layers": ["Аннотации и слои"], "Annotation layers": ["Слои аннотаций"], "My beautiful colors": ["Мои красивые цвета"], "< (Smaller than)": ["< (меньше чем)"], "> (Larger than)": ["> (больше чем)"], "<= (Smaller or equal)": ["<= (меньше или равно)"], ">= (Larger or equal)": [">= (больше или равно)"], "== (Is equal)": ["== (равно)"], "!= (Is not equal)": ["!= (не равно)"], "Not null": ["Не пусто"], "60 days": ["60 дней"], "90 days": ["90 дней"], "Send as PDF": ["Отправить как PDF"], "Send as PNG": ["Отправить в формате PNG"], "Send as CSV": ["Отправить в формате CSV"], "Send as text": ["Отправить текстом"], "General information": ["Общая информация"], "Alert condition": ["Условие оповещения"], "Alert contents": ["Содержание оповещения"], "Report contents": ["Содержание отчета"], "Notification method": ["Способ уведомления"], "owners": ["владельцы"], "content type": ["тип содержания"], "database": ["база данных"], "sql": ["sql"], "alert condition": ["условие оповещения"], "crontab": ["crontab"], "working timeout": ["работающий таймаут"], "recipients": ["получатели"], "email subject": ["тема письма"], "invalid email": ["неверный адрес электронной почты"], "Not all required fields are complete. Please provide the following:": ["Не все обязательные поля заполнены. Пожалуйста, укажите следующее:"], "Add another notification method": ["Добавить другой способ уведомления"], "Add delivery method": ["Добавить способ оповещения"], "report": ["рассылка"], "%s updated": ["Обновлено: %s"], "Edit Report": ["Редактировать отчет"], "Edit Alert": ["Редактировать оповещение"], "Add Report": ["Добавить рассылку"], "Add Alert": ["Добавить оповещение"], "Add": ["Добавить"], "Set up basic details, such as name and description.": ["Настройте основные сведения, такие как название и описание."], "Report name": ["Имя отчета"], "Alert name": ["Имя оповещения"], "Enter report name": ["Введите название отчета"], "Enter alert name": ["Введите название оповещения"], "Include description to be sent with %s": ["Включите описание, которое будет отправлено с %s"], "Report is active": ["Отчет активен"], "Alert is active": ["Оповещение активно"], "Define the database, SQL query, and triggering conditions for alert.": ["Определите базу данных, SQL-запрос и условия срабатывания оповещения."], "Select database": ["Выберите базу данных"], "SQL Query": ["SQL запрос"], "The result of this query must be a value capable of numeric interpretation e.g. 1, 1.0, or \"1\" (compatible with Python's float() function).": ["Результат этого запроса должен быть значением, способным к численной интерпретации, например, "], "Trigger Alert If...": ["Оповестить, если..."], "Condition": ["Условие"], "Customize data source, filters, and layout.": ["Настройте источник данных, фильтры и макет."], "Content type": ["Тип содержания"], "Select content type": ["Выбрать тип содержания"], "Select chart to use": ["Выберать график"], "Select dashboard": ["Выбрать дашборд"], "Select dashboard to use": ["Выбрать дашборд"], "Content format": ["Формат содержания"], "Select format": ["Выбрать формат"], "Screenshot width": ["<PERSON><PERSON><PERSON><PERSON>на скриншота"], "Input custom width in pixels": ["Ввод пользовательской ширины в пикселях"], "Ignore cache when generating report": ["Игнорировать кэш при создании отчета"], "Define delivery schedule, timezone, and frequency settings.": ["Определить расписание доставки, часовой пояс и настройки частоты."], "Timezone": ["Часовой пояс"], "Log retention": ["Хранение журнала"], "Working timeout": ["Время на рассылку"], "Time in seconds": ["Время в секундах"], "Grace period": ["Перерыв между оповещением"], "Choose notification method and recipients.": ["Выберите способ уведомления и получателей."], "Recurring (every)": ["Повторяющийся (каждый раз)"], "CRON Schedule": ["CRON расписание"], "Schedule type": ["Тип расписания"], "CRON expression": ["CRON выражение"], "Report sent": ["Отчет отправлен"], "Alert triggered, notification sent": ["Сработало оповещение, уведомление отправлено"], "Report sending": ["Отчет выполняется"], "Alert running": ["Выполняется оповещение"], "Report failed": ["Рассылка не удалась"], "Alert failed": ["Оповещение не сработало"], "Nothing triggered": ["Не срабатывало"], "Alert Triggered, In Grace Period": ["Оповещение сработало во время перерыва"], "CC recipients": ["Получатели CC"], "BCC recipients": ["BCC получатели"], "Email subject name (optional)": ["Название темы электронного письма (необязательно)"], "Please enter valid text. Spaces alone are not permitted.": ["Пожалуйста, введите действительный текст."], "Private Channels (Bot in channel)": ["частные каналы (бот в канале)"], "Notification Method": ["Способ уведомления"], "Delivery method": ["Способ оповещения"], "Select Delivery Method": ["Выберите способ оповещения"], "%s recipients": ["%s получателей"], "Recipients are separated by \",\" or \";\"": ["Получатели, разделенные \",\" или \";\""], "Select channels": ["Выберать каналы"], "Add CC Recipients": ["Добавить получателей CC"], "Add BCC Recipients": ["Добавить получателей BCC"], "Queries": ["Запросы"], "No entities have this tag currently assigned": ["Ни одной сущности не присвоен этот тег"], "Add tag to entities": ["Добавить тег к сущности"], "annotation_layer": ["слой_аннотации"], "Annotation template updated": ["Шаблон аннотации обновлен"], "Annotation template created": ["Шаблон аннотации создан"], "Edit annotation layer properties": ["Редактировать свойства слоя аннотаций"], "Annotation layer name": ["Имя слоя аннотаций"], "Description (this can be seen in the list)": ["Описание (будет видно в списке)"], "annotation": ["аннотация"], "The annotation has been updated": ["Аннотация обновлена"], "The annotation has been saved": ["Аннотация сохранена"], "Edit annotation": ["Редактировать аннотацию"], "Add annotation": ["Добавить аннотацию"], "date": ["дата"], "Additional information": ["Дополнительная информация"], "Please confirm": ["Пожалуйста, подтвердите действие"], "Are you sure you want to delete": ["Вы уверены, что хотите удалить"], "Modified %s": ["Изменено %s"], "css_template": ["css_template"], "Edit CSS template properties": ["Редактировать свойств CSS шаблона"], "Add CSS template": ["Добавить CSS шаблоны"], "css": ["css"], "published": ["опубликовано"], "draft": ["черновик"], "SQL Lab": ["Лаборатория SQL"], "Adjust how this database will interact with SQL Lab.": ["Настройка взаимодействия базы данных с Лабораторией SQL"], "Expose database in SQL Lab": ["Предоставить доступ к базе в Лаборатории SQL"], "Allow this database to be queried in SQL Lab": ["Разрешить запросы к этой базе данных в Лаборатории SQL"], "Allow creation of new tables based on queries": ["Разрешить создание новых таблиц на основе запросов"], "Allow creation of new views based on queries": ["Разрешить создание новых представлений на основе запросов"], "CTAS & CVAS SCHEMA": ["СХЕМА CTAS & CVAS"], "Create or select schema...": ["Создать или выбрать схему..."], "Force all tables and views to be created in this schema when clicking CTAS or CVAS in SQL Lab.": ["Принудить создание новых таблиц через CTAS или CVAS в Лаборатории SQL в этой схеме при нажатии соответствующих кнопок"], "Allow DDL and DML": ["Разрешить DDL и DML"], "Allow the execution of DDL (Data Definition Language: CREATE, DROP, TRUNCATE, etc.) and DML (Data Modification Language: INSERT, UPDATE, DELETE, etc)": ["Разрешить выполнение DDL (Data Definition Language: CREATE, DROP, TRUNCATE и т.д.) и DML (Data Modification Language: INSERT, UPDATE, DELETE и т.д.)."], "Enable query cost estimation": ["Разрешить оценку стоимости запроса"], "For Bigquery, Presto and Postgres, shows a button to compute cost before running a query.": ["<PERSON><PERSON><PERSON> Bigquery, Presto и Postgres, показывать кнопку подсчета стоимости запроса перед его выполнением."], "Allow this database to be explored": ["Разрешить изучение этой базы данных"], "When enabled, users are able to visualize SQL Lab results in Explore.": ["Если этот параметр включен, пользователи могут смотреть ответ запросов Лаборатории SQL в режиме исследования."], "Disable SQL Lab data preview queries": ["Отключить предпросмотр данных в Лаборатории SQL"], "Disable data preview when fetching table metadata in SQL Lab.  Useful to avoid browser performance issues when using  databases with very wide tables.": ["Отключить предварительный просмотр данных при извлечении метаданных таблицы в SQL Lab.  Отключить предварительный просмотр данных при извлечении метаданных таблицы в SQL Lab. Полезно для избежания проблем с производительностью браузера при использовании баз данных с очень широкими таблицами."], "Enable row expansion in schemas": ["Включить расширение строк в схемах"], "For Trino, describe full schemas of nested ROW types, expanding them with dotted paths": ["Для Trino опишите полные схемы вложенных типов ROW, расширяя их с помощью пунктирных линий"], "Performance": ["Производительность"], "Adjust performance settings of this database.": ["Настроить параметры производительности этой базы данных."], "Chart cache timeout": ["Время жизни кэша графика"], "Enter duration in seconds": ["Введите время в секундах"], "Duration (in seconds) of the caching timeout for charts of this database. A timeout of 0 indicates that the cache never expires, and -1 bypasses the cache. Note this defaults to the global timeout if undefined.": ["Продолжительность (в секундах) тайм -аута кэширования для графика этой базы данных. Длительность (в секундах) таймаута кэширования для графиков этой базы данных. Таймаут 0 означает, что кэш никогда не истекает, а -1 обходит кэш. Если значение не определено, по умолчанию используется глобальный таймаут."], "Schema cache timeout": ["Время жизни кэша схемы"], "Duration (in seconds) of the metadata caching timeout for schemas of this database. If left unset, the cache never expires.": ["Продолжительность (в секундах) таймаута кэша для схем этой базы данных. Обратите внимание, что если значение не задано, кэш никогда не очистится."], "Table cache timeout": ["Время жизни кэша таблицы"], "Duration (in seconds) of the metadata caching timeout for tables of this database. If left unset, the cache never expires. ": ["Длительность (в секундах) таймаута кэширования метаданных для таблиц этого типа базы данных. Если значение не задано, срок действия кэша не истекает."], "Asynchronous query execution": ["Асинхронное выполнение запросов"], "This option has been disabled by the administrator.": ["Администратор отключен этот вариант."], "Cancel query on window unload event": ["Отменять запрос при закрытии вкладки"], "Terminate running queries when browser window closed or navigated to another page. Available for Presto, Hive, MySQL, Postgres and Snowflake databases.": ["Завершать выполнение запросов после закрытия браузерной вкладки или пользователь переключился на другую вкладку. Доступно для баз данных Presto, Hive, MySQL, Postgres и Snowflake."], "Security": ["Безопасность"], "Add extra connection information.": ["Добавить дополнительную информация по подключению."], "Secure extra": ["Безопасность"], "JSON string containing additional connection configuration. This is used to provide connection information for systems like Hive, Presto and BigQuery which do not conform to the username:password syntax normally used by SQLAlchemy.": ["строка JSON, содержащая дополнительную конфигурацию соединения. JSON строка, содержащая дополнительную информацию о соединении. Это используется для указания информации о соединении с такими системами как Hive, Presto и BigQuery, которые не укладываются в шаблон \"пользователь:пароль\", который обычно используется в SQLAlchemy."], "Enter CA_BUNDLE": ["Введите CA_BUNDLE"], "Optional CA_BUNDLE contents to validate HTTPS requests. Only available on certain database engines.": ["Необязательное содержимое CA_BUNDLE для валидации HTTPS запросов. Доступно только в определенных драйверах баз данных."], "Impersonate logged in user (Presto, Trino, Drill, Hive, and GSheets)": ["Имперсонировать пользователя (Presto, Trino, Drill, Hive, и Google Таблицы)"], "If Presto or Trino, all the queries in SQL Lab are going to be executed as the currently logged on user who must have permission to run them. If Hive and hive.server2.enable.doAs is enabled, will run the queries as service account, but impersonate the currently logged on user via hive.server2.proxy.user property.": ["Если Presto или Trino, все запросы в SQL Lab будут выполнены в качестве в настоящее время зарегистрированного пользователя, который должен иметь разрешение на их запуск. Если вы используете Presto или Trino, все запросы в Лаборатории SQL будут выполняться от авторизованного пользователя, который должен иметь разрешение на их выполнение. Если включены Hive и hive.server2.enable.doAs, то запросы будут выполняться через техническую учетную запись, но имперсонировать зарегистрированного пользователя можно через свойство hive.server2.proxy.user."], "Allow file uploads to database": ["Разрешить загрузку файлов в базу данных"], "Schemas allowed for File upload": ["Схемы, в которые разрешена загрузка файлов"], "A comma-separated list of schemas that files are allowed to upload to.": ["Разделённый запятыми список схем, в которые можно загружать файлы."], "Additional settings.": ["Дополнительная настройка."], "Metadata Parameters": ["Параметры метаданных"], "The metadata_params object gets unpacked into the sqlalchemy.MetaData call.": ["Объект metadata_params вызывает sqlalchemy.MetaData"], "Engine Parameters": ["Параметры драйвера"], "The engine_params object gets unpacked into the sqlalchemy.create_engine call.": ["Объект engine_params вызывает sqlalchemy.create_engine"], "Version": ["Версия"], "Version number": ["Номер версии"], "Specify the database version. This is used with Presto for query cost estimation, and Dremio for syntax changes, among others.": ["Укажите версию базы данных. Это используется в Presto для оценки стоимости запросов, и Dremio для изменения синтаксиса, среди прочих."], "Disable drill to detail": ["Отключить drill to detail"], "Disables the drill to detail feature for this database.": ["Отключает функцию drill to detail для этой базы данных."], "Allow changing catalogs": ["Разрешить изменение каталогов"], "Give access to multiple catalogs in a single database connection.": ["Предоставьте доступ к нескольким каталогам через одно соединение с базой данных."], "STEP %(stepCurr)s OF %(stepLast)s": ["ШАГ %(stepCurr)s ИЗ %(stepLast)s"], "Enter Primary Credentials": ["Введите основные учетные данные"], "Need help? Learn how to connect your database": ["Нужна помощь? Узнайте, как подключаться к вашей базе данных"], "Database connected": ["Соединение с базой данных установлено"], "Create a dataset to begin visualizing your data as a chart or go to\n          SQL Lab to query your data.": ["Создайте датасет для визуализации ваших данных на графике или перейдите в Лабораторию SQL для просмотра данных."], "Enter the required %(dbModelName)s credentials": ["Введите обязательные данные для %(dbModelName)s"], "Need help? Learn more about": ["Нужна помощь? Узнайте больше о"], "connecting to %(dbModelName)s": ["подключение к %(dbModelName)s"], "Select a database to connect": ["Выберите базу данных для подключения"], "SSH Host": ["хост SSH"], "e.g. 127.0.0.1": ["например, 127.0.0.1"], "SSH Port": ["SSH порт"], "e.g. Analytics": ["например, Analytics"], "Login with": ["Войти при помощи"], "Password": ["Пароль"], "Private Key & Password": ["Приватный ключ и пароль"], "SSH Password": ["Пароль SSH"], "e.g. ********": ["например, ********"], "Private Key": ["Приват<PERSON><PERSON>й ключ"], "Paste Private Key here": ["вставьте личный ключ здесь"], "Private Key Password": ["Пароль приватного ключа"], "SSH Tunnel": ["SSH Туннель"], "SSH Tunnel configuration parameters": ["Параметры конфигурации SSH туннеля"], "Display Name": ["Отображаемое имя"], "Name your database": ["Дайте имя базе данных"], "Pick a name to help you identify this database.": ["Выберите имя для базы данных."], "dialect+driver://username:password@host:port/database": ["диалект+драйвер://пользователь:пароль@хост:порт/схема"], "Refer to the": ["Обратитесь к"], "for more information on how to structure your URI.": ["за подробной информацией по тому, как структурировать ваш URI."], "Test connection": ["Тестовое соединение"], "Please enter a SQLAlchemy URI to test": ["Введите SQLAlchemy URI для тестирования"], "e.g. world_population": ["например, health_medicine"], "Connection failed, please check your connection settings.": ["Соединение не удалось, проверьте настройки соединения."], "Database settings updated": ["Обновлены настройки базы данных"], "Sorry there was an error fetching database information: %s": ["К сожалению, произошла ошибка при получении информации о базе данных: %s"], "Or choose from a list of other databases we support:": ["Или выберите из списка других поддерживаемых баз данных:"], "Supported databases": ["Поддерживаемые базы данных"], "Choose a database...": ["Выберите базу данных..."], "Want to add a new database?": ["Хотите добавить новую базу данных?"], "Any databases that allow connections via SQL Alchemy URIs can be added. ": ["Можно добавлять любые базы данных, которые позволяют подключаться через URI SQL Alchemy. "], "Any databases that allow connections via SQL Alchemy URIs can be added. Learn about how to connect a database driver ": ["Любые базы данных, подключаемые через SQL Alchemy URI, могут быть добавлены. Узнайте больше о том, как подключить драйвер базы данных "], "Connect": ["Подключить"], "Finish": ["Завершить"], "This database is managed externally, and can't be edited in Superset": ["Эта база данных управляется извне и не может быть изменена в Суперсете"], "The passwords for the databases below are needed in order to import them. Please note that the \"Secure Extra\" and \"Certificate\" sections of the database configuration are not present in explore files and should be added manually after the import if they are needed.": ["Для их импорта необходимы пароли для приведенных ниже баз данных. Пароли к базам данных требуются, чтобы импортировать их. Пожалуйста, обратите внимание, что разделы \"Безопасность\" и \"Утверждение\" в настройках конфигурации базы данных отсутствуют в импортируемых файлах и должны быть добавлены вручную после импорта, если необходимо."], "You are importing one or more databases that already exist. Overwriting might cause you to lose some of your work. Are you sure you want to overwrite?": ["Вы импортируете одну или несколько баз данных, которые уже существуют. Перезапись может привести к потере части вашей работы. Вы уверены, что хотите перезаписать?"], "Database Creation Error": ["Ошибка создания базы данных"], "We are unable to connect to your database. Click \"See more\" for database-provided information that may help troubleshoot the issue.": ["Не удалось подключиться к базе данных. Нажмите \"Подробнее\" для информации, предоставленной базой данных в ответ, для решения проблемы."], "CREATE DATASET": ["СОЗДАТЬ ДАТАСЕТ"], "QUERY DATA IN SQL LAB": ["данные запроса в SQL Lab"], "Connect a database": ["Подключиться к базе данных"], "Edit database": ["Редактировать Базу Данных"], "Connect this database using the dynamic form instead": ["Подключиться к этой базе, используя динамичную форму"], "Click this link to switch to an alternate form that exposes only the required fields needed to connect this database.": ["Нажмите для переключения на альтернативную форму подключения, которая позволит вам ввести все данные в соответствующую форму для данной базы данных."], "Additional fields may be required": ["Могут потребоваться дополнительные поля"], "Select databases require additional fields to be completed in the Advanced tab to successfully connect the database. Learn what requirements your databases has ": ["Некоторые базы данных требуют ручной настройки во вкладке Продвинутая настройка для успешного подключения. Вы можете ознакомиться с требованиями к вашей базе данных "], "Import database from file": ["Импортировать базу данных из файла"], "Connect this database with a SQLAlchemy URI string instead": ["Подключиться к этой базе через SQLAlchemy URI"], "Click this link to switch to an alternate form that allows you to input the SQLAlchemy URL for this database manually.": ["Нажмите для переключения на альтернативную форму подключения, которая позволит вам вручную ввести SQLAlchemy URL для данной базы данных."], "This can be either an IP address (e.g. 127.0.0.1) or a domain name (e.g. mydatabase.com).": ["Это может быть как IP адрес (например, 127.0.0.1), так и доменное имя (например, моябазаданных.рф)."], "Host": ["Хо<PERSON>т"], "e.g. 5432": ["например, 5432"], "Port": ["Порт"], "e.g. sql/protocolv1/o/12345": ["например, "], "Copy the name of the HTTP Path of your cluster.": ["Скопируйте название пути HTTP вашего кластера."], "Database name": ["Имя базы данных"], "Copy the name of the database you are trying to connect to.": ["Впишите имя базы данных, к которой вы пытаетесь подключиться"], "e.g. hive_metastore": ["например, "], "Default Catalog": ["Каталог по умолчанию"], "The default catalog that should be used for the connection.": ["Каталог по умолчанию, который следует использовать для соединения."], "e.g. default": ["например, "], "Default Schema": ["Схема по умолчанию"], "The default schema that should be used for the connection.": ["Схема по умолчанию, которая должна использоваться для соединения."], "Paste your access token here": ["Вставьте здесь свой токен доступа"], "Access token": ["Токен доступа"], "Pick a nickname for how the database will display in Superset.": ["Выберите имя для базы данных, которое будет отображаться в Суперсете."], "e.g. param1=value1&param2=value2": ["например, параметр1=значение1&параметр2=значение2"], "Additional Parameters": ["Дополнительные параметры"], "Add additional custom parameters": ["Добавление дополнительных пользовательских параметров"], "SSL Mode \"require\" will be used.": ["SSL режим “require” будет использован."], "Type of Google Sheets allowed": ["Допустимый тип Google Таблиц"], "Publicly shared sheets only": ["Только общие листы"], "Public and privately shared sheets": ["Публичные и приватные с общим доступом листы"], "How do you want to enter service account credentials?": ["Как вы хотите ввести учетные данные об услугах?"], "Upload JSON file": ["Загрузить JSON файл"], "Copy and Paste JSON credentials": ["Скопировать и вставить JSON данные"], "Service Account": ["Сервисный аккаунт"], "Paste content of service credentials JSON file here": ["вставьте содержание файла сервисных учетных данных JSON здесь"], "Copy and paste the entire service account .json file here": ["Скопировать и вставить .json файл сервисного аккаунта сюда"], "Upload Credentials": ["Загрузить учетные данные"], "Use the JSON file you automatically downloaded when creating your service account.": ["Используйте файл JSON, который вы автоматически загружаете при создании учетной записи службы."], "Connect Google Sheets as tables to this database": ["Подключить Google Таблицы как таблицы для этой базы данных"], "Google Sheet Name and URL": ["Имя или URL Google Таблицы"], "Enter a name for this sheet": ["Введите название для этого листа"], "Paste the shareable Google Sheet URL here": ["вставьте общий URL -адрес листа Google здесь"], "Add sheet": ["Добавить лист"], "Copy the identifier of the account you are trying to connect to.": ["Скопируйте идентификатор учетной записи, к которой вы пытаетесь подключиться."], "e.g. xy12345.us-east-2.aws": ["например, "], "e.g. compute_wh": ["например, "], "e.g. AccountAdmin": ["например, "], "Upload file to preview columns": ["Загрузить файл для предварительного просмотра столбцов"], "Data Imported": ["Данные импортируются"], "Uploading a file is required": ["Загрузка файла требуется"], "Upload a file with a valid extension. Valid: [%s]": ["Загрузите файл с правильным расширением. Правильное: [%s]"], "Selecting a database is required": ["Требуется выбор базы данных"], "CSV Upload": ["Загрузка CSV"], "Excel Upload": ["Excel загрузка"], "Columnar Upload": ["Загрузка Columnar"], "Upload a file to a database.": ["Загрузите файл в базу данных."], "%(type)s File": ["%(type)s Файл"], "Preview uploaded file": ["Предварительный просмотр загруженный файл"], "Select a database": ["Выберите базу данных"], "Select a database to upload the file to": ["Выберите базу данных для загрузки файла"], "Select a schema": ["Выберите схему"], "Select a schema if the database supports this": ["Укажите схему, если она поддерживается базой данных"], "Name of table to be created": ["Название таблицы, которое будет создано"], "Delimiter": ["Разделитель"], "Select a delimiter for this data": ["Выберите разделитель для этих данных"], "Choose a delimiter": ["Выберите разделитель"], "Sheet name": ["Название листа"], "Choose sheet name": ["Выберите название листа"], "Select a sheet name from the uploaded file": ["Выберите имя листа из загруженного файла"], "File Settings": ["Настройки файла"], "Adjust how spaces, blank lines, null values are handled and other file wide settings.": ["Отрегули<PERSON><PERSON><PERSON>те, как пробелы, пустые строки, нулевые значения обрабатываются и другие настройки широких файлов."], "If Table Already Exists": ["Если таблица уже существует"], "What should happen if the table already exists": ["Что должно произойти, если таблица уже существует"], "Choose already exists": ["Выберите уже существует"], "Columns To Be Parsed as Dates": ["Список столбцов, которые должны быть интерпретированы как даты"], "Choose columns to be parsed as dates": ["Выберите столбцы, которые будут проанализированы в качестве дат"], "A comma separated list of columns that should be parsed as dates": ["Разделённый запятыми список столбцов, которые должны быть интерпретированы как даты."], "Decimal Character": ["Десятичный разделитель"], "Character to interpret as decimal point": ["Символ десятичного разделителя"], "Null Values": ["Пустые значения"], "Choose values that should be treated as null. Warning: Hive database supports only a single value": ["Выберите значения, которые следует рассматривать как нулевые."], "Skip spaces after delimiter": ["Пропускать пробелы после разделителя"], "Skip blank lines rather than interpreting them as Not A Number values": ["Пропускать пустые строки, вместо их перевода в пустые строки (NaN)"], "DD/MM format dates, international and European format": ["даты формата DD/MM, международный и европейский формат"], "Adjust column settings such as specifying the columns to read, how duplicates are handled, column data types, and more.": ["Настройте настройки столбцов, такие как указание столбцов для чтения, как обрабатываются дубликаты, типы данных столбцов и многое другое."], "Columns To Read": ["Столбцы для чтения"], "Choose columns to read": ["Выберите столбцы для чтения"], "List of the column names that should be read": ["Список имен столбцов, которые следует прочитать"], "Column Data Types": ["Типы данных столбцов"], "A dictionary with column names and their data types if you need to change the defaults. Example: {\"user_id\":\"int\"}. Check Python's Pandas library for supported data types.": ["Словарь с именами столбцов и типами данных, если вам нужно изменить значения по умолчанию."], "Column data types": ["Типы данных столбцов"], "Create dataframe index": ["Создать индекс данных"], "Index Column": ["Индексный столбец"], "Column to use as the index of the dataframe. If None is given, Index label is used.": ["Столбец для использования в качестве индекса DataFrame."], "Choose index column": ["Выберите столбец индекса"], "Index Label": ["Индексный ярлык"], "Label for the index column. Don't use an existing column name.": ["Метка для столбца индекса."], "Index label": ["Индексный ярлык"], "Set header rows and the number of rows to read or skip.": ["установите строки заголовка и количество рядов для чтения или пропуска."], "Header Row": ["Строка заголовка"], "Row containing the headers to use as column names (0 is first line of data).": ["Строка, содержащая заголовки для использования в качестве имен столбцов (0 - первая строка данных)."], "Header row": ["Заголовок заголовка"], "Rows to Read": ["Строки для чтения"], "Number of rows of file to read. Leave empty (default) to read all rows": ["Количество рядов файла для чтения."], "Rows to read": ["Строки для чтения"], "Skip Rows": ["Пропуск строк"], "Number of rows to skip at start of file.": ["Количество строк для пропуска в начале файла."], "Skip rows": ["Пропустить ряды"], "Duplicate dataset": ["Дублировать датасет"], "Duplicate": ["Дублировать"], "New dataset name": ["Новое имя датасета"], "The passwords for the databases below are needed in order to import them together with the datasets. Please note that the \"Secure Extra\" and \"Certificate\" sections of the database configuration are not present in export files, and should be added manually after the import if they are needed.": ["Для импорта их данных необходимы пароли для приведенных ниже баз данных. Пароли к базам данных требуются, чтобы импортировать их вместе с датасетами. Пожалуйста, обратите внимание, что разделы \"Безопасность\" и \"Утверждение\" конфигурации базы данных отсутствуют в экспортируемых файлах и должны быть добавлены после импорта вручную, если необходимо."], "You are importing one or more datasets that already exist. Overwriting might cause you to lose some of your work. Are you sure you want to overwrite?": ["Вы импортируете один или несколько датасетов, которые уже существуют. Перезапись может привести к потере части вашей работы. Вы уверены, что хотите продолжить?"], "Refreshing columns": ["Обновление столбцов"], "Table columns": ["Столбцы таблицы"], "Loading": ["Загрузка"], "This table already has a dataset associated with it. You can only associate one dataset with a table.\n": ["Эта таблица уже имеет датасет, связанный с ней. С одной таблицей можно связать только один датасет.\n"], "View Dataset": ["Посмотреть датасет"], "This table already has a dataset": ["Эта таблица уже имеет датасет"], "Datasets can be created from database tables or SQL queries. Select a database table to the left or ": ["Датасеты могут быть созданы из таблиц базы данных или SQL запросов. Выберите таблицу из базы данных слева или "], "create dataset from SQL query": ["создайте датасет из SQL запроса"], " to open SQL Lab. From there you can save the query as a dataset.": [" в Лаборатории SQL. Там вы сможете сохранить запрос как датасет."], "Select dataset source": ["Выберите источник датасета"], "No table columns": ["Нет столбцов таблицы"], "This database table does not contain any data. Please select a different table.": ["Эта таблица базы данных не содержит никаких данных."], "An Error Occurred": ["Произошла ошибка"], "Unable to load columns for the selected table. Please select a different table.": ["Не удалось загрузить столбцы для выбранной таблицы. Пожалуйста, выберите другую таблицу."], "The API response from %s does not match the IDatabaseTable interface.": ["ответ API от %s не соответствует идатабасетируемому интерфейсу."], "Usage": ["Использование"], "Chart owners": ["Владельцы графиков"], "Chart last modified": ["График изменен в последний раз"], "Chart last modified by": ["График изменен в последний раз"], "Dashboard usage": ["Использование дашборда"], "Create chart with dataset": ["Создать график с датасетом"], "chart": ["график"], "No charts": ["Нет графиков"], "This dataset is not used to power any charts.": ["Этот датасет не используется для построения каких-либо графиков."], "Select a database table.": ["Выберите таблицу в базе данных."], "Create dataset and create chart": ["Создать датасет и построить график"], "New dataset": ["Новый датасет"], "Select a database table and create dataset": ["Выберите базу данных и создайте датасет"], "dataset name": ["имя датасета"], "Not defined": ["Не определено"], "There was an error fetching dataset": ["Возникла ошибка при получении датасета"], "There was an error fetching dataset's related objects": ["Возникла ошибка при получении связанных объектов набора данных"], "There was an error loading the dataset metadata": ["Возникла ошибка при загрузке метаданных датасета"], "[Untitled]": ["[Без названия]"], "Unknown": ["Неизвестно"], "Viewed %s": ["Просмотрено %s"], "Edited": ["Редактировано"], "Created": ["Создано"], "Viewed": ["Просмотрено"], "Favorite": ["Избранное"], "Mine": ["Мои"], "View All »": ["Смотреть все »"], "dashboard": ["да<PERSON>борд"], "An error occurred while fetching dashboards: %s": ["Произошла ошибка при получении дашбордов: %s"], "charts": ["графики(ов)"], "dashboards": ["дашборды(ов)"], "recents": ["недавние(их)"], "saved queries": ["сохраненные(ых) запросы(ов)"], "No charts yet": ["Нет графиков"], "No dashboards yet": ["Нет дашбордов"], "No recents yet": ["Нет недавних записей еще"], "No saved queries yet": ["Нет сохраненных запросов"], "%(other)s charts will appear here": ["%(other)s графики будут появляться тут"], "%(other)s dashboards will appear here": ["%(other)s дашборды будут появляться тут"], "%(other)s recents will appear here": ["%(other)s недавние записи будут появляться тут"], "%(other)s saved queries will appear here": ["%(other)s сохраненные запросы будут появляться тут"], "Recently viewed charts, dashboards, and saved queries will appear here": ["Недавно просмотренные графики, дашборды и сохраненные запросы"], "Recently created charts, dashboards, and saved queries will appear here": ["Недавно созданные графики, дашборды и сохраненные запросы"], "Recently edited charts, dashboards, and saved queries will appear here": ["Недавно измененные графики, дашборды и сохраненные запросы"], "SQL query": ["SQL запрос"], "You don't have any favorites yet!": ["У вас пока нет избранных!"], "See all %(tableName)s": ["Список %(tableName)s"], "Connect database": ["Подключиться к базе данных"], "Create dataset": ["Создать датасет"], "Connect Google Sheet": ["Подключить Google Таблицы"], "Upload CSV to database": ["Загрузить файл CSV в базу данных"], "Upload Excel to database": ["Загрузить файл Excel в базу данных"], "Upload Columnar file to database": ["Загрузить файл столбчатого формата в базу данных"], "Enable 'Allow file uploads to database' in any database's settings": ["Включите \"Разрешить загрузку файлов в базу данных\" в настройках любой базы данных"], "Onboarding": ["Онбординг"], "Info": ["Личные данные"], "Logout": ["Выход из системы"], "About": ["О программе"], "Powered by Apache Superset": ["На базе Apache Superset"], "SHA": ["SHA"], "Build": ["Сборка"], "Documentation": ["Документация"], "Report a bug": ["Сообщить об ошибке"], "Login": ["Вход в систему"], "query": ["запрос"], "Deleted: %s": ["Удалено: %s"], "There was an issue deleting %s: %s": ["Произошла ошибка при удалении %s: %s"], "This action will permanently delete the saved query.": ["Это действие навсегда удалит сохранённый запрос."], "Delete Query?": ["Удалить запрос?"], "Saved queries": ["Сохраненные запросы"], "Tab name": ["Имя вкладки"], "User query": ["Пользовательский запрос"], "Executed query": ["Выполненный запрос"], "Query name": ["Имя запроса"], "SQL Copied!": ["SQL запрос скопирован!"], "Sorry, your browser does not support copying.": ["Извините, Ваш браузер не поддерживание копирование. Используйте сочетание клавиш [CTRL + C] для WIN или [CMD + C] для MAC."], "There was an issue fetching reports attached to this dashboard.": ["Произошла ошибка с получением рассылок, связанных с этим дашбордом."], "The report has been created": ["Рассылка создана"], "Report updated": ["Отчет обновлен"], "We were unable to active or deactivate this report.": ["Не удалось включить или выключить эту рассылку."], "Your report could not be deleted": ["Не удается удалить рассылку"], "Weekly Report for %s": ["Еженедельный отчет для %s"], "Weekly Report": ["Еженедельный отчет"], "Edit email report": ["Редактировать рассылку"], "Schedule a new email report": ["Запланировать новую рассылку по почте"], "Message content": ["Содержимое сообщения"], "Text embedded in email": ["Текст, включенный в email"], "Image (PNG) embedded in email": ["Изображение (PNG), встроенное в email"], "Formatted CSV attached in email": ["Форматированный CSV, прикрепленный к письму"], "Report Name": ["Имя отчета"], "Include a description that will be sent with your report": ["Описание, которое будет отправлено вместе с вашим отчетом"], "The report will be sent to your email at": ["Отчет будет отправлен на ваш адрес электронной почты по адресу"], "Failed to update report": ["Не удалось обновить отчет"], "Failed to create report": ["Не удалось создать рассылку"], "Set up an email report": ["Запланировать рассылку по почте"], "Email reports active": ["Включить рассылки"], "Delete email report": ["Удалить рассылку по email"], "Schedule email report": ["Запланировать рассылку по почте"], "This action will permanently delete %s.": ["Это действие навсегда удалит %s."], "Delete Report?": ["Удалить рассылку?"], "rowlevelsecurity": ["Безопасность на уровне строк"], "Rule added": ["Правило добавлено"], "Edit Rule": ["Редактировать правило"], "Add Rule": ["Добавить правило"], "Rule Name": ["Имя правила"], "The name of the rule must be unique": ["Имя правила должно быть уникальным"], "Regular filters add where clauses to queries if a user belongs to a role referenced in the filter, base filters apply filters to all queries except the roles defined in the filter, and can be used to define what users can see if no RLS filters within a filter group apply to them.": ["Регулярные фильтры добавляют, где положения, чтобы запросить, если пользователь принадлежит к роли, на которую ссылается фильтр, базовые фильтры применяют фильтры ко всем запросам, за исключением ролей, определенных в фильтре, и могут использоваться для определения того, какие пользователи могут видеть, не применяются ли RL -фильтры в группе фильтров.Обычные фильтры добавляют условия where к запросам, если пользователь принадлежит к роли указанной в фильтре, базовые фильтры применяют фильтры ко всем запросам кроме ролей, определенных в фильтре, и могут быть использованы для определения того, что пользователи могут видеть, если к ним не применяются фильтры RLS в группе фильтров."], "Datasets": ["Дата<PERSON><PERSON>ты"], "These are the datasets this filter will be applied to.": ["Это датасеты, к которым будет применяться данный фильтр."], "Excluded roles": ["Исключаемые роли роли"], "For regular filters, these are the roles this filter will be applied to. For base filters, these are the roles that the filter DOES NOT apply to, e.g. Admin if admin should see all data.": ["Для обычных фильтров это роли, к которым будет применяться данный фильтр. Для базовых фильтров это роли, к которым фильтр НЕ применяется, например, Admin, если admin должен видеть все данные."], "Group Key": ["Ключ группы"], "Filters with the same group key will be ORed together within the group, while different filter groups will be ANDed together. Undefined group keys are treated as unique groups, i.e. are not grouped together. For example, if a table has three filters, of which two are for departments Finance and Marketing (group key = 'department'), and one refers to the region Europe (group key = 'region'), the filter clause would apply the filter (department = 'Finance' OR department = 'Marketing') AND (region = 'Europe').": ["Фильтры с одним и тем же групповым ключом будут объединяться в группе, в то время как различные группы фильтров будут и будут вместе. Фильтры с одинаковым ключом группы будут объединены в группу по принципу OR, в то время как разные группы фильтров будут объединены AND. Неопределенные групповые  «ключи рассматриваются как уникальные группы, т.е. не группируются вместе. Например, если в таблице есть три фильтра, два из которых предназначены для отделов Финансы и маркетинг (ключ группы = 'department'), а один относится к региону Европа (ключ группы = 'region'), то в пункте фильтрации будет применен фильтр (department = 'Финансы' ИЛИ department = 'Маркетинг') И (region = 'Европа')»"], "Clause": ["Оператор"], "This is the condition that will be added to the WHERE clause. For example, to only return rows for a particular client, you might define a regular filter with the clause `client_id = 9`. To display no rows unless a user belongs to a RLS filter role, a base filter can be created with the clause `1 = 0` (always false).": ["Это условие, которое будет добавлено в предложение о том, где. Это условие, которое будет добавлено к оператору WHERE. Например, чтобы возвращать строки только для определенного клиента, вы можете определить обычный фильтр с условием `client_id = 9`. Чтобы не отображать строки, если пользователь не принадлежит к роли фильтра RLS, можно создать базовый фильтр с предложением `1 = 0` (всегда false)."], "Regular": ["Обычный"], "Base": ["Базовый"], "%s items could not be tagged because you don’t have edit permissions to all selected objects.": ["%s пункты не могут быть помечены, потому что у вас нет разрешений на редактирование для всех выбранных объектов."], "Tagged %s %ss": ["Протегировано %s %ss"], "Failed to tag items": ["Не удалось установить тег на объект"], "Bulk tag": ["Множественный выбор тегов"], "You are adding tags to %s %ss": ["вы добавляете теги к %s %ss"], "tags": ["теги"], "Select Tags": ["Выберите теги"], "Tag updated": ["Тег обновлен"], "Tag created": ["Тег создан"], "Tag name": ["Имя тега"], "Name of your tag": ["Назовите ваш тег"], "Add description of your tag": ["Добавить описание к вашему тегу"], "Select dashboards": ["Выберите дашборды"], "Select saved queries": ["Выберите сохраненные запросы"], "Chosen non-numeric column": ["Выбран нечисловой столбец"], "UI Configuration": ["Конфигурация UI"], "Filter value is required": ["Требуется значение фильтра"], "User must select a value before applying the filter": ["Для использования фильтра пользователь будет обязан выбрать значение"], "Single value": ["Единственное значение"], "Use only a single value.": ["Используйте только одно значение."], "Range filter plugin using AntD": ["Плагин фильтра диапазона с использованием ANTD"], "Experimental": ["Экспериментальный"], " (excluded)": [" (исключено)"], "%s option": ["%s вариант"], "Filter has data inconsistency. Check data source": ["В фильтре обнаружено несоответствие данных. Проверьте источник данных"], "Check for sorting ascending": ["Выберит для сортировки по возрастанию"], "Can select multiple values": ["Можно выбрать несколько значений"], "Select first filter value by default": ["Сделать первое значение фильтра значением по умолчанию"], "When using this option, default value can’t be set": ["При включении этой опции нельзя установить значение по умолчанию"], "Inverse selection": ["Выбрать противоположные значения"], "Exclude selected values": ["Исключить выбранные значения"], "Dynamically search all filter values": ["Динамически искать все значения фильтра"], "By default, each filter loads at most 1000 choices at the initial page load. Check this box if you have more than 1000 filter values and want to enable dynamically searching that loads filter values as users type (may add stress to your database).": ["По умолчанию каждый фильтр загружает не более 1000 вариантов на начальной загрузке страницы. По умолчанию, каждый фильтр загружает не больше 1000 элементов выбора при начальной загрузке страницы. Установите этот флаг, если у вас больше 1000 значений фильтра и вы хотите включить динамический поиск, который загружает значения по мере их ввода пользователем (может увеличить нагрузку на вашу базу данных)."], "Select by id with translation filter plugin using AntD'": ["Выберите ID с плагином фильтра «Перевод» с использованием ANTD '"], "Multiple IDs for one value": ["Несколько IDs для одного значения"], "Several IDs can be added into SQL query for one value": ["В SQL-запрос можно добавить несколько IDs для одного значения"], "Select by id filter plugin using AntD'": ["Выберите плагин с ID Filter с помощью ANTD '"], "Select with translation filter plugin using AntD'": ["Выберите с плагином фильтра «Перевод» с использованием ANTD '"], "Select filter plugin using AntD": ["Выберите плагин фильтра, используя ANTD"], "Custom time filter plugin": ["Пользовательский плагин фильтра времени"], "No time columns": ["Нет столбцов формата дата/время"], "Time column filter plugin": ["Плагин фильтра времени"], "Time grain filter plugin": ["Плагин фильтра времени"], "Working": ["Обрабатывается"], "Not triggered": ["Условие не выполнялось"], "On Grace": ["На перерыве"], "Alert": ["Оповещение"], "reports": ["рассылки"], "alerts": ["оповещений"], "There was an issue deleting the selected %s: %s": ["Произошла ошибка при удалении выбранных %s: %s"], "Last run": ["Последнее изменение"], "Active": ["Акти<PERSON><PERSON>н"], "Execution log": ["Жу<PERSON><PERSON>л выполнения"], "Bulk select": ["Множественный выбор"], "No %s yet": ["Пока нет %s"], "Owner": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "An error occurred while fetching owners values: %s": ["Произошла ошибка при получении владельцев графика: %s"], "Status": ["Статус"], "An error occurred while fetching dataset datasource values: %s": ["Произошла ошибка при получении значений датасета: %s"], "Alerts & reports": ["Оповещения и отчеты"], "Alerts": ["Оповещения"], "Reports": ["Отчеты"], "Delete %s?": ["Удалить %s?"], "Are you sure you want to delete the selected %s?": ["Вы уверены, что хотите удалить выбранные %s?"], "Error Fetching Tagged Objects": ["Ошибка извлечения объектов с меткой"], "Edit Tag": ["Редактировать тег"], "There was an issue deleting the selected layers: %s": ["Произошла ошибка при удалении выбранных слоёв: %s"], "Edit template": ["Редактировать шаблон"], "Delete template": ["Удалить шаблон"], "Changed by": ["Изменилось"], "No annotation layers yet": ["Пока нет слоев аннотаций"], "This action will permanently delete the layer.": ["Это действие навсегда удалит слой."], "Delete Layer?": ["Удалить слой?"], "Are you sure you want to delete the selected layers?": ["Вы уверены, что хотите удалить выбранные слои?"], "There was an issue deleting the selected annotations: %s": ["Произошла ошибка при удалении выбранных аннотаций: %s"], "Delete annotation": ["Удалить аннотацию"], "Annotation": ["Аннотация"], "No annotation yet": ["Пока нет аннотаций"], "Annotation Layer %s": ["Слой аннотаций %s"], "Back to all": ["Вернуться ко всем"], "Are you sure you want to delete %s?": ["Вы уверены, что хотите удалить %s?"], "Delete Annotation?": ["Удалить аннотацию?"], "Are you sure you want to delete the selected annotations?": ["Вы уверены, что хотите удалить выбранные аннотации?"], "Failed to load chart data": ["Не удалось загрузить данные графика"], "view instructions": ["смотреть инструкцию"], "Add a dataset": ["Добавить датасет"], "Choose a dataset": ["Выберите датасет"], "Choose chart type": ["Выберите тип графика"], "Please select both a Dataset and a Chart type to proceed": ["Пожалуйста, для продолжения выберите и датасет, и тип графика"], "The passwords for the databases below are needed in order to import them together with the charts. Please note that the \"Secure Extra\" and \"Certificate\" sections of the database configuration are not present in export files, and should be added manually after the import if they are needed.": ["Для баз данных нужны пароли, чтобы импортировать их вместе с графиками. Пожалуйста, обратите внимание, что разделы \"Безопасность\" и \"Утверждение\" в настройках конфигурации базы данных отсутствуют в экспортируемых файлов и должны быть добавлены вручную после импорта, если необходимо."], "You are importing one or more charts that already exist. Overwriting might cause you to lose some of your work. Are you sure you want to overwrite?": ["Вы импортируете один или несколько графиков, которые уже существуют. Перезапись может привести к потере части вашей работы. Вы уверены, что хотите перезаписать?"], "Chart imported": ["График импортирован"], "There was an issue deleting the selected charts: %s": ["Произошла ошибка при удалении выбранных графиков: %s"], "An error occurred while fetching dashboards": ["Произошла ошибка при получении дашбордов"], "Tag": ["Тег"], "An error occurred while fetching chart owners values: %s": ["Произошла ошибка при получении владельцев графика: %s"], "Certified": ["Утверждено"], "Alphabetical": ["В алфавитном порядке"], "Recently modified": ["Измененные недавно"], "Least recently modified": ["Измененные давно"], "Import charts": ["Импортировать графики"], "Are you sure you want to delete the selected charts?": ["Вы уверены, что хотите удалить выбранные графики?"], "CSS templates": ["CSS шаблоны"], "There was an issue deleting the selected templates: %s": ["Произошла ошибка при удалении выбранных шаблонов: %s"], "CSS template": ["CSS шаблон"], "This action will permanently delete the template.": ["Это действие навсегда удалит шаблон."], "Delete Template?": ["Удалить шаблон?"], "Are you sure you want to delete the selected templates?": ["Вы уверены, что хотите удалить выбранные шаблоны?"], "The passwords for the databases below are needed in order to import them together with the dashboards. Please note that the \"Secure Extra\" and \"Certificate\" sections of the database configuration are not present in export files, and should be added manually after the import if they are needed.": ["Для баз данных нужны пароли, чтобы импортировать их вместе с дашбордами. Пожалуйста, обратите внимание, что разделы \"Безопасность\" и \"Утверждение\" в настройках конфигурации базы данных отсутствуют в экспортируемых файлах и должны быть добавлены вручную после импорта, если необходимо."], "You are importing one or more dashboards that already exist. Overwriting might cause you to lose some of your work. Are you sure you want to overwrite?": ["Вы импортируете один или несколько дашбордов, которые уже существуют. Перезапись может привести к потере части вашей работы. Вы уверены, что хотите перезаписать?"], "Dashboard imported": ["Дашборд импортирован"], "There was an issue deleting the selected dashboards: ": ["Произошла ошибка при удалении выбранных дашбордов: "], "An error occurred while fetching dashboard owner values: %s": ["Произошла ошибка при получении владельца дашборда: %s"], "Are you sure you want to delete the selected dashboards?": ["Вы уверены, что хотите удалить выбранные дашборды?"], "An error occurred while fetching database related data: %s": ["Произошла ошибка при получении данных о базе данных: %s"], "Upload file to database": ["Загрузить файл в базу данных"], "Upload CSV": ["Загрузить CSV"], "Upload Excel": ["Загрузить Excel"], "Upload Columnar": ["Загрузить Columnar"], "AQE": ["Асинхронные запросы"], "Allow data manipulation language": ["Разрешить операции вставки, обновления и удаления данных"], "DML": ["DML"], "File upload": ["Загрузка файлов"], "Delete database": ["Удалить базу данных"], "The database %s is linked to %s charts that appear on %s dashboards and users have %s SQL Lab tabs using this database open. Are you sure you want to continue? Deleting the database will break those objects.": ["База данных %s привязана к %s графику(-ам), который(-ые) используется(-ются) в %s дашборде(-ах), и пользователи имеют %s открытую(-ых) вкладку(-ок) в Лаборатории SQL. Вы уверены, что хотите продолжить? Удаление базы данных приведёт к неработоспособности этих компонентов."], "Delete Database?": ["Удалить базу данных?"], "Dataset imported": ["Импортирован датасет"], "An error occurred while fetching dataset related data": ["Произошла ошибка при получении метаданных датасета"], "An error occurred while fetching dataset related data: %s": ["Произошла ошибка при получении данных о датасете: %s"], "Physical dataset": ["Физический датасет"], "Virtual dataset": ["Виртуальный датасет"], "Virtual": ["Виртуальный"], "Search by query text": ["Поиск по тексту запроса"], "An error occurred while fetching datasets: %s": ["Произошла ошибка при получении датасетов: %s"], "An error occurred while fetching schema values: %s": ["Произошла ошибка при извлечении значений схемы: %s"], "An error occurred while fetching dataset owner values: %s": ["Произошла ошибка при получении владельца датасета: %s"], "Import datasets": ["Импортировать датасеты"], "There was an issue deleting the selected datasets: %s": ["Произошла ошибка при удалении выбранных датасетов: %s"], "There was an issue duplicating the dataset.": ["Произошла ошибка при дублировании датасета."], "There was an issue duplicating the selected datasets: %s": ["Произошла ошибка при дублировании выбранных датасетов: %s"], "Also the dataset is linked to": ["Также датасет привязан к"], "%s dashboard filters.": ["%s дашборд фильтру(-ам)."], "The dataset %s is linked to": ["Датасет %s привязан к"], "%s charts": ["%s графику(-ам) "], "that appear on": [", который(-ые) используется(-ются) в"], "%s dashboards.": ["%s дашборде(-ах)."], "Are you sure you want to continue? Deleting the dataset will break those objects.": ["Вы уверены, что хотите продолжить? Удаление датасета приведёт к неработоспособности этих объектов."], "The dataset %s is linked to %s charts that appear on %s dashboards. Are you sure you want to continue? Deleting the dataset will break those objects.": ["Датасет %s привязан к %s графику(-ам), который(-ые) используется(-ются) в %s дашборде(-ах). Вы уверены, что хотите продолжить? Удаление датасета приведёт к неработоспособности этих объектов."], "Delete Dataset?": ["Удалить датасет?"], "Are you sure you want to delete the selected datasets?": ["Вы уверены, что хотите удалить выбранные датасеты?"], "0 Selected": ["0 выбрано"], "%s Selected (Virtual)": ["%s Выбрано (Виртуальные)"], "%s Selected (Physical)": ["%s Выбрано (Физические)"], "%s Selected (%s Physical, %s Virtual)": ["%s Выбрано (%s Физические, %s Виртуальные)"], "log": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Execution ID": ["ID исполнения"], "Scheduled at (UTC)": ["Запланировано на (часовой пояс UTC)"], "Start at (UTC)": ["Время начала (UTC)"], "Error message": ["Сообщение об ошибке"], "There was an issue fetching your recent activity: %s": ["Произошла ошибка при получении вашей последней активности: %s"], "Home": ["Главная"], "Thumbnails": ["Миниа<PERSON><PERSON><PERSON>ы"], "Recents": ["Недавние"], "There was an issue previewing the selected query. %s": ["Произошла ошибка при предпросмотре выбранного запроса: %s"], "TABLES": ["ТАБЛИЦЫ"], "Open query in SQL Lab": ["Открыть в SQL редакторе"], "An error occurred while fetching database values: %s": ["Произошла ошибка при получении значений базы данных: %s"], "An error occurred while fetching user values: %s": ["Произошла ошибка при извлечении пользовательских значений: %s"], "Row Level Security": ["Безопасность на уровне строк"], "Deleted %s": ["Удалено %s"], "There was an issue deleting rules: %s": ["Возникла проблема с удалением правил: %s"], "No Rules yet": ["Правила отсутствуют"], "Are you sure you want to delete the selected rules?": ["Вы уверены, что хотите удалить выбранные правила?"], "The passwords for the databases below are needed in order to import them together with the saved queries. Please note that the \"Secure Extra\" and \"Certificate\" sections of the database configuration are not present in export files, and should be added manually after the import if they are needed.": ["Для баз данных нужны пароли, чтобы импортировать их вместе с сохраненными запросами. Пожалуйста, обратите внимание, что разделы \"Безопасность\" и \"Утверждение\" в настройках конфигурации базы данных отсутствуют в экспортируемых файлах и должны быть добавлены вручную после импорта, если необходимо."], "You are importing one or more saved queries that already exist. Overwriting might cause you to lose some of your work. Are you sure you want to overwrite?": ["Вы импортируете один или несколько сохраненных запросов, которые уже существуют. Перезапись может привести к потере части вашей работы. Вы уверены, что хотите продолжить?"], "Query imported": ["Запрос импортирован"], "There was an issue previewing the selected query %s": ["Произошла ошибка при предпросмотре выбранного запроса %s"], "Import queries": ["Импортировать запросы"], "Link Copied!": ["Ссылка скопирована!"], "There was an issue deleting the selected queries: %s": ["Произошла ошибка при удалении выбранных запросов: %s"], "Edit query": ["Редактировать запрос"], "Copy query URL": ["Скопировать ссылку на запрос"], "Export query": ["Экспорт запроса"], "Delete query": ["Удалить запрос"], "Searches all text fields: Name, Description, Database & Schema": ["Поиск по всем текстовым полям: Название, Описание, База данных и Схема"], "Are you sure you want to delete the selected queries?": ["Вы уверены, что хотите удалить выбранные запросы?"], "queries": ["запросы"], "tag": ["тег"], "No Tags created": ["Нет созданных Тегов"], "Are you sure you want to delete the selected tags?": ["Вы уверены, что хотите удалить выбранные теги?"], "Image download failed, please refresh and try again.": ["Ошибка скачивания изображения, пожалуйста, обновите и попробуйте заново."], "PDF download failed, please refresh and try again.": ["Загрузка PDF не удалась, пожалуйста, обновите и попробуйте снова."], "Select values in highlighted field(s) in the control panel. Then run the query by clicking on the %s button.": ["Выберите значения в обязательных полях на панели управления. Затем запустите запрос, нажав на кнопку %s."], "An error occurred while fetching %s info: %s": ["Произошла ошибка при получении информации о %s: %s"], "An error occurred while fetching %ss: %s": ["Произошла ошибка при получении: %s: %s"], "An error occurred while creating %ss: %s": ["Произошла ошибка при создании %ss: %s"], "Please re-export your file and try importing again": ["Пожалуйста, повторно экспортируйте ваш файл и попробуйте импортировать его снова"], "An error occurred while importing %s: %s": ["Произошла ошибка при попытке импортировать %s: %s"], "There was an error fetching the favorite status: %s": ["Произошла ошибка при получении статуса избранного: %s"], "There was an error saving the favorite status: %s": ["Произошла ошибка при сохранении статуса избранного: %s"], "Connection looks good!": ["Соединение в порядке!"], "ERROR: %s": ["ОШИБКА: %s"], "There was an error fetching the filtered charts and dashboards:": ["Возникла ошибка при получении отфильтрованных графиков и дашбордов:"], "There was an issue deleting: %s": ["Произошла ошибка при удалении: %s"], "URL": ["Ссылка (URL)"], "Templated link, it's possible to include {{ metric }} or other values coming from the controls.": ["Шаблонная ссылка, можно включить {{ metric }} или другие значения, поступающие из элементов управления."], "Time-series Table": ["Таблица временных рядов"], "Compare multiple time series charts (as sparklines) and related metrics quickly.": ["Быстрое сравнение нескольких графиков временных рядов (в виде спарклайнов) и связанных с ними показателей."], "Text": ["Текст"], "We have the following keys: %s": ["У нас есть следующие ключи: %s"], "Query History": ["История запросов"], "Manage": ["Управление"], "List Users": ["Список пользователей"], "List Roles": ["Список ролей"], "User Registrations": ["Регистрации пользователей"], "Action Log": ["<PERSON><PERSON><PERSON><PERSON><PERSON> Действий"], "Database Connections": ["Базы данных"], "Plugins": ["Плагины"], "CSS Templates": ["CSS шаблоны"], "Alerts & Reports": ["Оповещения и отчеты"], "Drop columns here or click": ["Перетащите столбцы сюда"], "Saved Queries": ["Сохраненные Запросы"], "Select All": ["Выбрать все"], "Day": ["День"], "Week": ["Неделя"], "Month": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "Quarter": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "Year": ["Год"], "Week starting sunday": ["Неделя с воскресенья"], "Week starting monday": ["Неделя с понедельника"], "Week ending saturday": ["Неделя до субботы"], "Week_ending sunday": ["Неделя до воскресенья"], "Week starting Sunday": ["Неделя с воскресенья"], "Week starting Monday": ["Неделя с понедельника"], "Week ending Saturday": ["Неделя до субботы"], "Managing Company": ["Управляющая компания"], "Create data": ["Подготовка данных"], "Vizualize data": ["Визуализация данных"], "center": ["По центру"], "oldest": ["Первое"], "average": ["Среднее"], "latest": ["Последнее"], "changes": ["изменение"], "vs": ["против"]}}}