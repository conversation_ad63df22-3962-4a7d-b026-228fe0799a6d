# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
import logging

from flask import request, Response
from flask_appbuilder.api import expose, protect, safe
from marshmallow import ValidationError

from superset.csi.feedback.schemas import (
    FeedbackCreateSchema,
    FeedbackResponseSchema,
    openapi_spec_methods_override,
)
from superset.csi.models import CSI
from superset.csi.utils import can_user_vote
from superset.daos.csi import CSIDAO
from superset.daos.dashboard import <PERSON><PERSON><PERSON><PERSON>
from superset.extensions import security_manager
from superset.utils.core import get_user
from superset.views.base_api import BaseSupersetApi, requires_json

logger = logging.getLogger(__name__)


class CSIFeedbackRestApi(BaseSupersetApi):
    """
    CSI Feedback REST API for collecting customer feedback
    """

    resource_name = "csi_feedback"
    allow_browser_login = True
    openapi_spec_tag = "CSI Feedback"
    openapi_spec_methods = openapi_spec_methods_override

    @expose("/can-vote", methods=("GET",))
    @protect()
    @safe
    def can_vote(self) -> Response:
        """Check if user can vote for a dashboard.
        ---
        get:
          summary: Check if user can vote
          description: >-
            Checks if the current user can submit feedback for a dashboard
            based on CSI configuration and previous votes.
          parameters:
          - in: query
            name: dashboard_id
            schema:
              type: integer
            required: true
            description: Dashboard ID to check
          - in: query
            name: standalone
            schema:
              type: boolean
            required: false
            description: Whether this is standalone mode (default: true)
          responses:
            200:
              description: Vote eligibility status
              content:
                application/json:
                  schema:
                    type: object
                    properties:
                      can_vote:
                        type: boolean
                        description: Whether user can vote
                      reason:
                        type: string
                        description: Reason if cannot vote
            400:
              $ref: '#/components/responses/400'
            401:
              $ref: '#/components/responses/401'
            500:
              $ref: '#/components/responses/500'
        """
        try:
            dashboard_id = request.args.get("dashboard_id", type=int)
            standalone = (
                request.args.get("standalone", type=str, default="true").lower()
                == "true"
            )

            if not dashboard_id:
                return self.response_400(message="dashboard_id parameter is required")

            can_vote, reason = can_user_vote(dashboard_id, standalone)
            return self.response(200, can_vote=can_vote, reason=reason)

        except Exception as ex:  # pylint: disable=broad-exception-caught
            logger.exception("Error checking vote eligibility: %s", str(ex))
            return self.response_500(message=str(ex))

    @expose("/", methods=("GET",))
    @protect()
    @safe
    def get_list(self) -> Response:
        """Get feedback list.
        ---
        get:
          summary: Get feedback list
          description: >-
            Returns a list of feedback entries. Can be filtered by dashboard_id.
          parameters:
          - in: query
            name: dashboard_id
            schema:
              type: integer
            description: Filter feedback by dashboard ID
          responses:
            200:
              description: Feedback list
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/FeedbackListResponseSchema'
            401:
              $ref: '#/components/responses/401'
            500:
              $ref: '#/components/responses/500'
        """
        try:
            dashboard_id = request.args.get("dashboard_id", type=int)
            dashboard = DashboardDAO.find_by_id(dashboard_id)
            if not security_manager.is_owner(dashboard):
                return self.response_403()

            csi_entries = CSIDAO.find_by_dashboard_id(dashboard_id)

            response_schema = FeedbackResponseSchema(many=True)
            result = response_schema.dump(csi_entries)
            return self.response(200, result=result, count=len(result))

        except Exception as ex:  # pylint: disable=broad-exception-caught
            logger.exception("Error getting feedback list: %s", str(ex))
            return self.response_500(message=str(ex))

    @expose("/summary", methods=("GET",))
    @protect()
    @safe
    def get_summary(self) -> Response:
        """Get feedback summary.
        ---
        get:
          summary: Get feedback summary
          description: >-
            Returns a summary of feedback entries. Can be filtered by dashboard_id.
          parameters:
          - in: query
            name: dashboard_id
            schema:
              type: integer
            description: Filter feedback by dashboard ID
        """
        try:
            dashboard_id = request.args.get("dashboard_id", type=int)
            dashboard = DashboardDAO.find_by_id(dashboard_id)
            if not security_manager.is_owner(dashboard):
                return self.response_403()

            summary = CSIDAO.get_summary(dashboard_id)
            return self.response(200, result=summary)
        except Exception as ex:  # pylint: disable=broad-exception-caught
            logger.exception("Error getting feedback summary: %s", str(ex))
            return self.response_500(message=str(ex))

    @expose("/", methods=("POST",))
    @protect()
    @safe
    @requires_json
    def post(self) -> Response:
        """Create new feedback.
        ---
        post:
          summary: Create feedback
          description: >-
            Creates a new feedback entry.
          requestBody:
            description: Feedback data
            required: true
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/FeedbackCreateSchema'
          responses:
            201:
              description: Feedback created
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/FeedbackResponseSchema'
            400:
              $ref: '#/components/responses/400'
            401:
              $ref: '#/components/responses/401'
            500:
              $ref: '#/components/responses/500'
        """
        try:
            feedback_schema = FeedbackCreateSchema()
            feedback_data = feedback_schema.load(request.json)

            dashboard_id = feedback_data["dashboard_id"]
            user = get_user()
            if user is None or user.is_anonymous:
                return self.response_401()

            is_standalone = feedback_data["standalone"]

            can_vote, reason = can_user_vote(dashboard_id, is_standalone, user.id)
            if not can_vote:
                return self.response_400(message=reason)

            voter_hash = CSI.generate_voter_hash(user.id, dashboard_id)

            csi = CSIDAO.create_feedback(
                user_id=user.id if not feedback_data["anonymous"] else None,
                dashboard_id=dashboard_id,
                rating=feedback_data["rating"],
                standalone=is_standalone,
                comment=feedback_data.get("comment"),
                voter_hash=voter_hash,
            )

            schema = FeedbackResponseSchema()
            result = schema.dump(csi)
            return self.response(201, **result)

        except ValidationError as error:
            return self.response_400(message=error.messages)
        except Exception as ex:  # pylint: disable=broad-exception-caught
            logger.exception("Error creating feedback: %s", str(ex))
            return self.response_500(message=str(ex))
