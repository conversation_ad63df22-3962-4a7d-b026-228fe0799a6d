# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
from marshmallow import fields, Schema, validate

openapi_spec_methods_override = {
    "get": {"get": {"summary": "Get feedback by ID"}},
    "get_list": {"get": {"summary": "Get feedback list"}},
    "post": {"post": {"summary": "Create feedback"}},
}


class FeedbackCreateSchema(Schema):
    dashboard_id = fields.Integer(required=True, description="Dashboard ID")
    rating = fields.Integer(
        required=True,
        validate=validate.Range(min=1, max=10),
        description="Rating from 1 to 10",
    )
    comment = fields.String(
        validate=validate.Length(max=500),
        description="Feedback comment",
        allow_none=True,
    )
    standalone = fields.Boolean(
        required=True, description="Whether this is standalone feedback"
    )
    anonymous = fields.Boolean(
        required=True, description="Whether this is anonymous feedback"
    )


class FeedbackResponseSchema(Schema):
    dashboard_id = fields.Integer(description="Dashboard ID")
    rating = fields.Integer(description="Rating 1-10")
    standalone = fields.Boolean(description="Whether this is standalone feedback")
    comment = fields.String(description="Feedback comment", allow_none=True)
    created_on = fields.DateTime(description="Creation timestamp", allow_none=True)
