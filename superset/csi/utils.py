# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

from typing import Optional

from superset.csi.models import CSI
from superset.daos.csi import CSIConfigDAO, CSIDAO
from superset.utils.core import get_user_id


def can_user_vote(
    dashboard_id: int, standalone: bool, user_id: Optional[int] = None
) -> tuple[bool, str]:
    """
    Check if a user can vote for a dashboard.

    Args:
        dashboard_id: Dashboard ID to check
        standalone: Whether this is standalone mode
        user_id: User ID (if None, uses current user)

    Returns:
        Tuple of (can_vote: bool, reason: str)
    """
    if user_id is None:
        user_id = get_user_id()

    if not user_id:
        return False, "User not authenticated"

    # Check if CSI is enabled for this dashboard
    csi_config = CSIConfigDAO.find_by_dashboard_id(dashboard_id)
    if not csi_config:
        return False, "CSI not configured for this dashboard"

    # Check if CSI is enabled for the current environment
    if standalone and not csi_config.standalone_enabled:
        return False, "CSI not enabled for standalone version"
    if not standalone and not csi_config.plugin_enabled:
        return False, "CSI not enabled for plugin version"

    # Check if user already voted
    voter_hash = CSI.generate_voter_hash(user_id, dashboard_id)
    existing_vote = CSIDAO.find_by_voter_hash(voter_hash)

    if existing_vote:
        return False, "User already voted for this dashboard"

    return True, "User can vote"
