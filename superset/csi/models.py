# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

import hashlib
from datetime import datetime
from typing import Any

from flask_appbuilder import Model
from sqlalchemy import Boolean, Column, DateTime, ForeignKey, Integer, String
from sqlalchemy.orm import relationship

from superset import security_manager


class CSIConfig(Model):
    """Model for CSI configuration per dashboard"""

    __tablename__ = "csi_config"

    id = Column(Integer, primary_key=True)
    dashboard_id = Column(
        Integer,
        ForeignKey("dashboards.id", ondelete="CASCADE"),
        nullable=False,
        unique=True,
    )
    standalone_enabled = Column(Boolean, nullable=False, default=False)
    plugin_enabled = Column(Boolean, nullable=False, default=False)
    created_on = Column(DateTime, default=datetime.now, nullable=True)

    # Relationships
    dashboard = relationship(
        "Dashboard", foreign_keys=[dashboard_id], backref="csi_config"
    )

    def __repr__(self) -> str:
        return f"<CSIConfig(dashboard_id={self.dashboard_id}, standalone={self.standalone_enabled}, plugin={self.plugin_enabled})>"

    @property
    def data(self) -> dict[str, Any]:
        """Return a dictionary representation of the CSI config"""
        return {
            "id": self.id,
            "dashboard_id": self.dashboard_id,
            "standalone_enabled": self.standalone_enabled,
            "plugin_enabled": self.plugin_enabled,
            "created_on": self.created_on.isoformat() if self.created_on else None,
        }


class CSI(Model):
    """Model for collecting customer service interface feedback"""

    __tablename__ = "csi"

    id = Column(Integer, primary_key=True)
    user_id = Column(
        Integer, ForeignKey("ab_user.id", ondelete="SET NULL"), nullable=True
    )
    dashboard_id = Column(
        Integer, ForeignKey("dashboards.id", ondelete="CASCADE"), nullable=False
    )
    rating = Column(Integer, nullable=True)
    standalone = Column(Boolean, nullable=False, default=False)
    comment = Column(String(500), nullable=True)
    voter_hash = Column(
        String(64), nullable=True
    )  # SHA256 hash for duplicate prevention
    created_on = Column(DateTime, default=datetime.now, nullable=True)

    # Relationships
    user = relationship(
        security_manager.user_model, foreign_keys=[user_id], backref="csi_entries"
    )
    dashboard = relationship(
        "Dashboard", foreign_keys=[dashboard_id], backref="csi_entries"
    )

    def __repr__(self) -> str:
        return f"<CSI(id={self.id}, dashboard_id={self.dashboard_id}, rating={self.rating})>"

    @staticmethod
    def generate_voter_hash(user_id: int, dashboard_id: int) -> str:
        """Generate voter hash for duplicate prevention"""
        data = f"{user_id}-{dashboard_id}"
        return hashlib.sha256(data.encode()).hexdigest()

    @property
    def data(self) -> dict[str, Any]:
        """Return a dictionary representation of the CSI entry"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "dashboard_id": self.dashboard_id,
            "rating": self.rating,
            "standalone": self.standalone,
            "comment": self.comment,
            "created_on": self.created_on.isoformat() if self.created_on else None,
        }
