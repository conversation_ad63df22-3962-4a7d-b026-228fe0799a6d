# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
from marshmallow import fields, Schema

openapi_spec_methods_override = {
    "get": {"get": {"summary": "Get CSI configuration by dashboard ID"}},
    "get_list": {"get": {"summary": "Get all CSI configurations"}},
    "put": {"put": {"summary": "Update CSI configuration"}},
}


class CSIConfigUpdateSchema(Schema):
    standalone_enabled = fields.Boolean(
        description="Enable CSI for standalone version", allow_none=True
    )
    plugin_enabled = fields.Boolean(
        description="Enable CSI for plugin version", allow_none=True
    )


class CSIConfigGetResponseSchema(Schema):
    id = fields.Integer(description="Config ID", allow_none=True)
    dashboard_id = fields.Integer(description="Dashboard ID")
    standalone_enabled = fields.Boolean(description="CSI enabled for standalone")
    plugin_enabled = fields.Boolean(description="CSI enabled for plugin")
    created_on = fields.String(description="Creation timestamp", allow_none=True)


class CSIConfigListResponseSchema(Schema):
    result = fields.List(
        fields.Nested(CSIConfigGetResponseSchema),
        description="List of CSI configurations",
    )
    count = fields.Integer(description="Total number of CSI configurations")
