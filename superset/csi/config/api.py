# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
import logging

from flask import request, Response
from flask_appbuilder.api import expose, protect, safe
from marshmallow import ValidationError

from superset.csi.config.schemas import (
    CSIConfigGetResponseSchema,
    CSIConfigUpdateSchema,
    openapi_spec_methods_override,
)
from superset.daos.csi import CSIConfigDAO
from superset.daos.dashboard import DashboardDAO
from superset.extensions import security_manager
from superset.views.base_api import BaseSupersetApi, requires_json

logger = logging.getLogger(__name__)


class CSIConfigRestApi(BaseSupersetApi):
    """
    CSI Config REST API for managing CSI configuration settings
    """

    resource_name = "csi_config"
    allow_browser_login = True
    openapi_spec_tag = "CSI Config"
    openapi_spec_methods = openapi_spec_methods_override

    @expose("/<int:dashboard_id>", methods=("GET",))
    @protect()
    @safe
    def get(self, dashboard_id: int) -> Response:
        """Get CSI configuration for a specific dashboard.
        ---
        get:
          summary: Get CSI configuration by dashboard ID
          description: >-
            Returns CSI configuration for a specific dashboard.
          parameters:
          - in: path
            name: dashboard_id
            schema:
              type: integer
            description: Dashboard ID
          responses:
            200:
              description: CSI configuration
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/CSIConfigGetResponseSchema'
            404:
              $ref: '#/components/responses/404'
            401:
              $ref: '#/components/responses/401'
            500:
              $ref: '#/components/responses/500'
        """
        try:
            config = CSIConfigDAO.find_by_dashboard_id(dashboard_id)

            default_config = {
                "dashboard_id": dashboard_id,
                "standalone_enabled": False,
                "plugin_enabled": False,
                "created_on": None,
            }
            schema = CSIConfigGetResponseSchema()
            result = schema.dump(config.data if config else default_config)

            return self.response(200, **result)

        except Exception as ex:  # pylint: disable=broad-exception-caught
            logger.exception(
                "Error getting CSI config for dashboard %s: %s", dashboard_id, str(ex)
            )
            return self.response_500(message=str(ex))

    @expose("/<int:dashboard_id>", methods=("PUT",))
    @protect()
    @safe
    @requires_json
    def put(self, dashboard_id: int) -> Response:
        """Update CSI configuration for a dashboard.
        ---
        put:
          summary: Update CSI configuration
          description: >-
            Updates CSI configuration for a specific dashboard.
          parameters:
          - in: path
            name: dashboard_id
            schema:
              type: integer
            description: Dashboard ID
          requestBody:
            description: CSI configuration data
            required: true
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/CSIConfigUpdateSchema'
          responses:
            200:
              description: CSI configuration updated
              content:
                application/json:
                  schema:
                    $ref: '#/components/schemas/CSIConfigGetResponseSchema'
            400:
              $ref: '#/components/responses/400'
            401:
              $ref: '#/components/responses/401'
            500:
              $ref: '#/components/responses/500'
        """
        try:
            config_schema = CSIConfigUpdateSchema()
            config_data = config_schema.load(request.json)

            dashboard = DashboardDAO.find_by_id(dashboard_id)
            if not security_manager.is_owner(dashboard):
                return self.response_403()

            config = CSIConfigDAO.create_or_update(
                dashboard_id=dashboard_id,
                standalone_enabled=config_data.get("standalone_enabled", False),
                plugin_enabled=config_data.get("plugin_enabled", False),
            )

            schema = CSIConfigGetResponseSchema()
            result = schema.dump(config.data)
            return self.response(200, **result)

        except ValidationError as error:
            return self.response_400(message=error.messages)
        except Exception as ex:  # pylint: disable=broad-exception-caught
            logger.exception(
                "Error updating CSI config for dashboard %s: %s", dashboard_id, str(ex)
            )
            return self.response_500(message=str(ex))
